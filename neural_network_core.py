"""
Core Neural Network Implementation - Full Python
Unified implementation for both XOR and Digit Recognition
"""

import numpy as np
import pickle
import os
from typing import List, Tuple, Dict, Any
# import matplotlib.pyplot as plt  # Optional dependency
# import seaborn as sns  # Optional dependency
from dataclasses import dataclass
import time

@dataclass
class TrainingConfig:
    """Configuration for neural network training"""
    learning_rate: float = 0.01
    epochs: int = 1000
    batch_size: int = 32
    early_stopping_patience: int = 50
    early_stopping_threshold: float = 1e-6
    validation_split: float = 0.2

class ActivationFunction:
    """Collection of activation functions and their derivatives"""
    
    @staticmethod
    def sigmoid(x):
        """Sigmoid activation function"""
        # Clip x to prevent overflow
        x = np.clip(x, -500, 500)
        return 1 / (1 + np.exp(-x))
    
    @staticmethod
    def sigmoid_derivative(x):
        """Sigmoid derivative"""
        return x * (1 - x)
    
    @staticmethod
    def tanh(x):
        """Tanh activation function"""
        return np.tanh(x)
    
    @staticmethod
    def tanh_derivative(x):
        """Tanh derivative"""
        return 1 - x * x
    
    @staticmethod
    def relu(x):
        """ReLU activation function"""
        return np.maximum(0, x)
    
    @staticmethod
    def relu_derivative(x):
        """ReLU derivative"""
        return (x > 0).astype(float)
    
    @staticmethod
    def leaky_relu(x, alpha=0.01):
        """Leaky ReLU activation function"""
        return np.where(x > 0, x, alpha * x)
    
    @staticmethod
    def leaky_relu_derivative(x, alpha=0.01):
        """Leaky ReLU derivative"""
        return np.where(x > 0, 1, alpha)
    
    @staticmethod
    def softmax(x):
        """Softmax activation function"""
        # Subtract max for numerical stability
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)

class UnifiedNeuralNetwork:
    """
    Unified Neural Network implementation for both XOR and Digit Recognition
    Supports multiple architectures and activation functions
    """
    
    def __init__(self, 
                 architecture: List[int],
                 activation_function: str = 'sigmoid',
                 output_activation: str = 'sigmoid',
                 config: TrainingConfig = None):
        """
        Initialize neural network
        
        Args:
            architecture: List of layer sizes [input, hidden1, hidden2, ..., output]
            activation_function: Activation function for hidden layers
            output_activation: Activation function for output layer
            config: Training configuration
        """
        self.architecture = architecture
        self.activation_function = activation_function
        self.output_activation = output_activation
        self.config = config or TrainingConfig()
        
        # Initialize weights and biases
        self.weights = []
        self.biases = []
        self._initialize_weights()
        
        # Training history
        self.training_history = {
            'loss': [],
            'accuracy': [],
            'val_loss': [],
            'val_accuracy': [],
            'epoch_times': []
        }
        
        # Network state for visualization
        self.activations = []
        self.z_values = []
        
        # Training state
        self.is_trained = False
        self.best_weights = None
        self.best_biases = None
        self.best_loss = float('inf')
        
    def _initialize_weights(self):
        """Initialize weights using Xavier/He initialization"""
        for i in range(len(self.architecture) - 1):
            input_size = self.architecture[i]
            output_size = self.architecture[i + 1]
            
            # Xavier initialization for sigmoid/tanh, He initialization for ReLU
            if self.activation_function in ['relu', 'leaky_relu']:
                # He initialization
                std = np.sqrt(2.0 / input_size)
            else:
                # Xavier initialization
                std = np.sqrt(2.0 / (input_size + output_size))
            
            weight = np.random.normal(0, std, (input_size, output_size))
            bias = np.zeros((1, output_size))
            
            self.weights.append(weight)
            self.biases.append(bias)
    
    def _get_activation_function(self, layer_type='hidden'):
        """Get activation function based on layer type"""
        activation_name = self.activation_function if layer_type == 'hidden' else self.output_activation
        
        activation_map = {
            'sigmoid': (ActivationFunction.sigmoid, ActivationFunction.sigmoid_derivative),
            'tanh': (ActivationFunction.tanh, ActivationFunction.tanh_derivative),
            'relu': (ActivationFunction.relu, ActivationFunction.relu_derivative),
            'leaky_relu': (ActivationFunction.leaky_relu, ActivationFunction.leaky_relu_derivative),
            'softmax': (ActivationFunction.softmax, None)
        }
        
        return activation_map.get(activation_name, activation_map['sigmoid'])
    
    def forward(self, X, store_activations=True):
        """
        Forward propagation
        
        Args:
            X: Input data (batch_size, input_size)
            store_activations: Whether to store activations for visualization
            
        Returns:
            Output predictions
        """
        if store_activations:
            self.activations = [X]
            self.z_values = []
        
        current_input = X
        
        for i, (weight, bias) in enumerate(zip(self.weights, self.biases)):
            # Linear transformation
            z = np.dot(current_input, weight) + bias
            
            # Activation function
            if i == len(self.weights) - 1:  # Output layer
                activation_func, _ = self._get_activation_function('output')
            else:  # Hidden layers
                activation_func, _ = self._get_activation_function('hidden')
            
            a = activation_func(z)
            
            if store_activations:
                self.z_values.append(z)
                self.activations.append(a)
            
            current_input = a
        
        return current_input
    
    def backward(self, X, y):
        """
        Backward propagation
        
        Args:
            X: Input data
            y: Target labels
        """
        m = X.shape[0]
        
        # Forward pass to get activations
        output = self.forward(X)
        
        # Initialize gradients
        dW = [np.zeros_like(w) for w in self.weights]
        db = [np.zeros_like(b) for b in self.biases]
        
        # Output layer error
        if self.output_activation == 'softmax':
            # For softmax with cross-entropy loss
            dz = output - y
        else:
            # For other activations with MSE loss
            _, output_derivative = self._get_activation_function('output')
            dz = (output - y) * output_derivative(output)
        
        # Backpropagate through layers
        for i in reversed(range(len(self.weights))):
            # Gradients for weights and biases
            dW[i] = (1/m) * np.dot(self.activations[i].T, dz)
            db[i] = (1/m) * np.sum(dz, axis=0, keepdims=True)
            
            # Error for previous layer (if not input layer)
            if i > 0:
                da_prev = np.dot(dz, self.weights[i].T)
                _, activation_derivative = self._get_activation_function('hidden')
                dz = da_prev * activation_derivative(self.activations[i])
        
        # Update weights and biases
        for i in range(len(self.weights)):
            self.weights[i] -= self.config.learning_rate * dW[i]
            self.biases[i] -= self.config.learning_rate * db[i]
    
    def calculate_loss(self, X, y):
        """Calculate loss"""
        predictions = self.forward(X, store_activations=False)
        
        if self.output_activation == 'softmax':
            # Cross-entropy loss
            # Add small epsilon to prevent log(0)
            epsilon = 1e-15
            predictions = np.clip(predictions, epsilon, 1 - epsilon)
            return -np.mean(np.sum(y * np.log(predictions), axis=1))
        else:
            # Mean squared error
            return np.mean((predictions - y) ** 2)
    
    def calculate_accuracy(self, X, y):
        """Calculate accuracy"""
        predictions = self.forward(X, store_activations=False)
        
        if len(y.shape) > 1 and y.shape[1] > 1:
            # Multi-class classification
            pred_labels = np.argmax(predictions, axis=1)
            true_labels = np.argmax(y, axis=1)
        else:
            # Binary classification
            pred_labels = (predictions > 0.5).astype(int).flatten()
            true_labels = y.flatten()
        
        return np.mean(pred_labels == true_labels)
    
    def train(self, X_train, y_train, X_val=None, y_val=None, verbose=True):
        """
        Train the neural network
        
        Args:
            X_train: Training data
            y_train: Training labels
            X_val: Validation data
            y_val: Validation labels
            verbose: Whether to print training progress
        """
        if X_val is None:
            # Split training data for validation
            val_size = int(len(X_train) * self.config.validation_split)
            indices = np.random.permutation(len(X_train))
            
            val_indices = indices[:val_size]
            train_indices = indices[val_size:]
            
            X_val = X_train[val_indices]
            y_val = y_train[val_indices]
            X_train = X_train[train_indices]
            y_train = y_train[train_indices]
        
        # Reset training history
        self.training_history = {
            'loss': [],
            'accuracy': [],
            'val_loss': [],
            'val_accuracy': [],
            'epoch_times': []
        }
        
        patience_counter = 0
        
        for epoch in range(self.config.epochs):
            start_time = time.time()
            
            # Shuffle training data
            indices = np.random.permutation(len(X_train))
            X_train_shuffled = X_train[indices]
            y_train_shuffled = y_train[indices]
            
            # Mini-batch training
            for i in range(0, len(X_train), self.config.batch_size):
                batch_X = X_train_shuffled[i:i + self.config.batch_size]
                batch_y = y_train_shuffled[i:i + self.config.batch_size]
                
                self.backward(batch_X, batch_y)
            
            # Calculate metrics
            train_loss = self.calculate_loss(X_train, y_train)
            train_acc = self.calculate_accuracy(X_train, y_train)
            val_loss = self.calculate_loss(X_val, y_val)
            val_acc = self.calculate_accuracy(X_val, y_val)
            
            epoch_time = time.time() - start_time
            
            # Store history
            self.training_history['loss'].append(train_loss)
            self.training_history['accuracy'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_accuracy'].append(val_acc)
            self.training_history['epoch_times'].append(epoch_time)
            
            # Early stopping
            if val_loss < self.best_loss:
                self.best_loss = val_loss
                self.best_weights = [w.copy() for w in self.weights]
                self.best_biases = [b.copy() for b in self.biases]
                patience_counter = 0
            else:
                patience_counter += 1
            
            # Print progress
            if verbose and (epoch % 10 == 0 or epoch == self.config.epochs - 1):
                print(f"Epoch {epoch:4d}: "
                      f"Loss: {train_loss:.4f}, Acc: {train_acc:.3f}, "
                      f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.3f}, "
                      f"Time: {epoch_time:.2f}s")
            
            # Early stopping check
            if patience_counter >= self.config.early_stopping_patience:
                if verbose:
                    print(f"Early stopping at epoch {epoch}")
                break
            
            # Loss threshold check
            if train_loss < self.config.early_stopping_threshold:
                if verbose:
                    print(f"Loss threshold reached at epoch {epoch}")
                break
        
        # Restore best weights
        if self.best_weights is not None:
            self.weights = self.best_weights
            self.biases = self.best_biases
        
        self.is_trained = True
        
        if verbose:
            final_acc = self.calculate_accuracy(X_val, y_val)
            print(f"Training completed! Final validation accuracy: {final_acc:.3f}")
        
        return self.training_history
    
    def predict(self, X):
        """Make predictions"""
        return self.forward(X, store_activations=False)
    
    def predict_single(self, x):
        """Predict single sample with detailed output"""
        if len(x.shape) == 1:
            x = x.reshape(1, -1)
        
        probabilities = self.predict(x)[0]
        
        if len(probabilities) > 1:
            # Multi-class
            predicted_class = np.argmax(probabilities)
            confidence = probabilities[predicted_class]
            
            return {
                'predicted_class': int(predicted_class),
                'confidence': float(confidence),
                'probabilities': probabilities.tolist()
            }
        else:
            # Binary
            confidence = float(probabilities[0])
            predicted_class = int(confidence > 0.5)
            
            return {
                'predicted_class': predicted_class,
                'confidence': confidence if predicted_class else 1 - confidence,
                'probabilities': [1 - confidence, confidence]
            }
    
    def save_model(self, filepath):
        """Save model to file"""
        model_data = {
            'architecture': self.architecture,
            'activation_function': self.activation_function,
            'output_activation': self.output_activation,
            'weights': self.weights,
            'biases': self.biases,
            'config': self.config,
            'training_history': self.training_history,
            'is_trained': self.is_trained
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"Model saved to {filepath}")
    
    def load_model(self, filepath):
        """Load model from file"""
        if not os.path.exists(filepath):
            print(f"Model file {filepath} not found!")
            return False
        
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.architecture = model_data['architecture']
            self.activation_function = model_data['activation_function']
            self.output_activation = model_data['output_activation']
            self.weights = model_data['weights']
            self.biases = model_data['biases']
            self.config = model_data['config']
            self.training_history = model_data['training_history']
            self.is_trained = model_data['is_trained']
            
            print(f"Model loaded from {filepath}")
            return True
            
        except Exception as e:
            print(f"Error loading model: {e}")
            return False
    
    def get_network_state(self):
        """Get current network state for visualization"""
        return {
            'architecture': self.architecture,
            'weights': self.weights,
            'biases': self.biases,
            'activations': self.activations if hasattr(self, 'activations') else [],
            'training_history': self.training_history
        }
