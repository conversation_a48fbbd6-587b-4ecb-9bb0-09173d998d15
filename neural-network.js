class NeuralNetwork {
    constructor(inputSize, hiddenSize, outputSize, learningRate = 0.1, activationFunction = 'sigmoid') {
        this.inputSize = inputSize;
        this.hiddenSize = hiddenSize;
        this.outputSize = outputSize;
        this.learningRate = learningRate;
        this.activationFunction = activationFunction;
        
        // Initialize weights and biases
        this.weightsInputHidden = this.initializeWeights(inputSize, hiddenSize);
        this.weightsHiddenOutput = this.initializeWeights(hiddenSize, outputSize);
        this.biasHidden = this.initializeBias(hiddenSize);
        this.biasOutput = this.initializeBias(outputSize);
        
        // Store activations for visualization
        this.inputActivations = [];
        this.hiddenActivations = [];
        this.outputActivations = [];
        
        // Training history
        this.lossHistory = [];
        this.epoch = 0;
    }
    
    initializeWeights(rows, cols) {
        const weights = [];
        for (let i = 0; i < rows; i++) {
            weights[i] = [];
            for (let j = 0; j < cols; j++) {
                // Xavier initialization
                weights[i][j] = (Math.random() - 0.5) * 2 * Math.sqrt(6 / (rows + cols));
            }
        }
        return weights;
    }
    
    initializeBias(size) {
        return new Array(size).fill(0).map(() => (Math.random() - 0.5) * 0.1);
    }
    
    // Activation functions
    sigmoid(x) {
        return 1 / (1 + Math.exp(-x));
    }
    
    sigmoidDerivative(x) {
        return x * (1 - x);
    }
    
    tanh(x) {
        return Math.tanh(x);
    }
    
    tanhDerivative(x) {
        return 1 - x * x;
    }
    
    relu(x) {
        return Math.max(0, x);
    }
    
    reluDerivative(x) {
        return x > 0 ? 1 : 0;
    }
    
    activate(x) {
        switch (this.activationFunction) {
            case 'sigmoid':
                return this.sigmoid(x);
            case 'tanh':
                return this.tanh(x);
            case 'relu':
                return this.relu(x);
            default:
                return this.sigmoid(x);
        }
    }
    
    activateDerivative(x) {
        switch (this.activationFunction) {
            case 'sigmoid':
                return this.sigmoidDerivative(x);
            case 'tanh':
                return this.tanhDerivative(x);
            case 'relu':
                return this.reluDerivative(x);
            default:
                return this.sigmoidDerivative(x);
        }
    }
    
    // Forward propagation
    forward(inputs) {
        this.inputActivations = [...inputs];
        
        // Input to hidden layer
        this.hiddenActivations = [];
        for (let i = 0; i < this.hiddenSize; i++) {
            let sum = this.biasHidden[i];
            for (let j = 0; j < this.inputSize; j++) {
                sum += inputs[j] * this.weightsInputHidden[j][i];
            }
            this.hiddenActivations[i] = this.activate(sum);
        }
        
        // Hidden to output layer
        this.outputActivations = [];
        for (let i = 0; i < this.outputSize; i++) {
            let sum = this.biasOutput[i];
            for (let j = 0; j < this.hiddenSize; j++) {
                sum += this.hiddenActivations[j] * this.weightsHiddenOutput[j][i];
            }
            this.outputActivations[i] = this.activate(sum);
        }
        
        return this.outputActivations;
    }
    
    // Backward propagation
    backward(inputs, targets) {
        const outputs = this.forward(inputs);
        
        // Calculate output layer errors
        const outputErrors = [];
        for (let i = 0; i < this.outputSize; i++) {
            outputErrors[i] = (targets[i] - outputs[i]) * this.activateDerivative(outputs[i]);
        }
        
        // Calculate hidden layer errors
        const hiddenErrors = [];
        for (let i = 0; i < this.hiddenSize; i++) {
            let error = 0;
            for (let j = 0; j < this.outputSize; j++) {
                error += outputErrors[j] * this.weightsHiddenOutput[i][j];
            }
            hiddenErrors[i] = error * this.activateDerivative(this.hiddenActivations[i]);
        }
        
        // Update weights and biases
        // Hidden to output weights
        for (let i = 0; i < this.hiddenSize; i++) {
            for (let j = 0; j < this.outputSize; j++) {
                this.weightsHiddenOutput[i][j] += this.learningRate * outputErrors[j] * this.hiddenActivations[i];
            }
        }
        
        // Input to hidden weights
        for (let i = 0; i < this.inputSize; i++) {
            for (let j = 0; j < this.hiddenSize; j++) {
                this.weightsInputHidden[i][j] += this.learningRate * hiddenErrors[j] * inputs[i];
            }
        }
        
        // Update biases
        for (let i = 0; i < this.outputSize; i++) {
            this.biasOutput[i] += this.learningRate * outputErrors[i];
        }
        
        for (let i = 0; i < this.hiddenSize; i++) {
            this.biasHidden[i] += this.learningRate * hiddenErrors[i];
        }
    }
    
    // Train the network
    train(trainingData, epochs = 1000) {
        for (let epoch = 0; epoch < epochs; epoch++) {
            let totalLoss = 0;
            
            for (let i = 0; i < trainingData.length; i++) {
                const inputs = trainingData[i].input;
                const targets = trainingData[i].output;
                
                this.backward(inputs, targets);
                
                // Calculate loss
                const outputs = this.forward(inputs);
                for (let j = 0; j < outputs.length; j++) {
                    totalLoss += Math.pow(targets[j] - outputs[j], 2);
                }
            }
            
            const avgLoss = totalLoss / trainingData.length;
            this.lossHistory.push(avgLoss);
            this.epoch++;
            
            // Early stopping if loss is very small
            if (avgLoss < 0.001) {
                break;
            }
        }
    }
    
    // Predict
    predict(inputs) {
        return this.forward(inputs);
    }
    
    // Calculate accuracy
    calculateAccuracy(testData) {
        let correct = 0;
        for (let i = 0; i < testData.length; i++) {
            const prediction = this.predict(testData[i].input);
            const predicted = prediction[0] > 0.5 ? 1 : 0;
            const actual = testData[i].output[0];
            if (predicted === actual) {
                correct++;
            }
        }
        return (correct / testData.length) * 100;
    }
    
    // Reset network
    reset() {
        this.weightsInputHidden = this.initializeWeights(this.inputSize, this.hiddenSize);
        this.weightsHiddenOutput = this.initializeWeights(this.hiddenSize, this.outputSize);
        this.biasHidden = this.initializeBias(this.hiddenSize);
        this.biasOutput = this.initializeBias(this.outputSize);
        this.lossHistory = [];
        this.epoch = 0;
        this.inputActivations = [];
        this.hiddenActivations = [];
        this.outputActivations = [];
    }
    
    // Get network state for visualization
    getNetworkState() {
        return {
            inputActivations: this.inputActivations,
            hiddenActivations: this.hiddenActivations,
            outputActivations: this.outputActivations,
            weightsInputHidden: this.weightsInputHidden,
            weightsHiddenOutput: this.weightsHiddenOutput,
            lossHistory: this.lossHistory,
            epoch: this.epoch
        };
    }
}
