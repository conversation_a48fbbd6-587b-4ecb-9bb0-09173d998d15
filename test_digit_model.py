#!/usr/bin/env python3
"""
Test script untuk digit recognition model
Menguji model yang sudah dilatih dengan sample data
"""

import numpy as np
from digit_model import DigitNeuralNetwork, create_synthetic_mnist_data

def test_model():
    """Test the trained digit recognition model"""
    print("🧠 Testing Digit Recognition Model")
    print("=" * 50)
    
    # Load the trained model
    model = DigitNeuralNetwork()
    if not model.load_model('digit_model.pkl'):
        print("❌ Failed to load model. Please train the model first.")
        return
    
    print("✅ Model loaded successfully!")
    print(f"📊 Model is trained: {model.is_trained}")
    
    # Create test data
    print("\n📝 Creating test data...")
    X_test, y_test = create_synthetic_mnist_data(100)
    
    # Test accuracy
    print("\n🎯 Testing model accuracy...")
    accuracy = model.calculate_accuracy(X_test, y_test)
    print(f"📈 Test Accuracy: {accuracy:.1%}")
    
    # Test individual predictions
    print("\n🔍 Testing individual predictions...")
    print("-" * 50)
    
    correct_predictions = 0
    total_predictions = 10
    
    for i in range(total_predictions):
        # Get a test sample
        test_image = X_test[i]
        true_label = np.argmax(y_test[i])
        
        # Make prediction
        result = model.predict_single(test_image)
        predicted_digit = result['predicted_digit']
        confidence = result['confidence']
        
        # Check if correct
        is_correct = predicted_digit == true_label
        if is_correct:
            correct_predictions += 1
        
        status = "✅" if is_correct else "❌"
        print(f"{status} Sample {i+1}: True={true_label}, Predicted={predicted_digit}, Confidence={confidence:.1%}")
    
    print("-" * 50)
    print(f"🎯 Individual Test Results: {correct_predictions}/{total_predictions} correct ({correct_predictions/total_predictions:.1%})")
    
    # Show confidence distribution for one sample
    print("\n📊 Detailed prediction for sample 1:")
    result = model.predict_single(X_test[0])
    true_digit = np.argmax(y_test[0])
    
    print(f"True digit: {true_digit}")
    print(f"Predicted digit: {result['predicted_digit']}")
    print(f"Confidence: {result['confidence']:.1%}")
    print("\nAll predictions:")
    
    for digit, prob in enumerate(result['all_predictions']):
        bar = "█" * int(prob * 20)  # Visual bar
        print(f"  {digit}: {prob:.3f} {bar}")
    
    return model

def visualize_sample_predictions(model, num_samples=5):
    """Visualize some sample predictions"""
    print(f"\n🖼️  Visualizing {num_samples} sample predictions...")
    
    # Create test data
    X_test, y_test = create_synthetic_mnist_data(num_samples)
    
    try:
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(1, num_samples, figsize=(15, 3))
        if num_samples == 1:
            axes = [axes]
        
        for i in range(num_samples):
            # Get prediction
            result = model.predict_single(X_test[i])
            true_digit = np.argmax(y_test[i])
            
            # Reshape image for display
            image = X_test[i].reshape(28, 28)
            
            # Plot
            axes[i].imshow(image, cmap='gray')
            axes[i].set_title(f'True: {true_digit}\nPred: {result["predicted_digit"]}\nConf: {result["confidence"]:.1%}')
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig('sample_predictions.png', dpi=150, bbox_inches='tight')
        print("📸 Sample predictions saved as 'sample_predictions.png'")
        
    except ImportError:
        print("⚠️  Matplotlib not available. Skipping visualization.")

def benchmark_model():
    """Benchmark model performance"""
    print("\n⚡ Benchmarking model performance...")
    
    model = DigitNeuralNetwork()
    if not model.load_model('digit_model.pkl'):
        print("❌ Failed to load model.")
        return
    
    # Create larger test set
    X_test, y_test = create_synthetic_mnist_data(1000)
    
    import time
    
    # Benchmark prediction speed
    start_time = time.time()
    predictions = []
    
    for i in range(100):  # Test 100 predictions
        result = model.predict_single(X_test[i])
        predictions.append(result)
    
    end_time = time.time()
    avg_time = (end_time - start_time) / 100
    
    print(f"⏱️  Average prediction time: {avg_time*1000:.2f} ms")
    print(f"🚀 Predictions per second: {1/avg_time:.0f}")
    
    # Calculate overall accuracy
    accuracy = model.calculate_accuracy(X_test, y_test)
    print(f"📊 Overall accuracy on 1000 samples: {accuracy:.1%}")

if __name__ == "__main__":
    try:
        # Test the model
        model = test_model()
        
        if model:
            # Visualize predictions
            visualize_sample_predictions(model)
            
            # Benchmark performance
            benchmark_model()
            
            print("\n🎉 Testing completed successfully!")
            print("\n💡 Tips:")
            print("   - Model accuracy should be > 40% for synthetic data")
            print("   - Try training with more epochs for better accuracy")
            print("   - Real MNIST data would give much better results")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
