"""
Streamlit Application for Neural Network Visualization
Full Python implementation with interactive interface
"""

import streamlit as st
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import time
import io
import base64
from PIL import Image, ImageDraw
import cv2

# Import our custom modules
from neural_network_core import UnifiedNeuralNetwork, TrainingConfig, ActivationFunction
from data_generators import DataGenerator
from visualization_utils import NetworkVisualizer

# Set page config
st.set_page_config(
    page_title="🧠 Neural Network Simulator",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #ff7f0e;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'model' not in st.session_state:
        st.session_state.model = None
    if 'training_history' not in st.session_state:
        st.session_state.training_history = None
    if 'X_train' not in st.session_state:
        st.session_state.X_train = None
    if 'y_train' not in st.session_state:
        st.session_state.y_train = None
    if 'X_test' not in st.session_state:
        st.session_state.X_test = None
    if 'y_test' not in st.session_state:
        st.session_state.y_test = None
    if 'canvas_data' not in st.session_state:
        st.session_state.canvas_data = None

def create_drawing_canvas():
    """Create a drawing canvas for digit input"""
    # Canvas for drawing
    canvas_html = """
    <div style="text-align: center;">
        <canvas id="drawingCanvas" width="280" height="280" 
                style="border: 2px solid #ddd; border-radius: 10px; cursor: crosshair; background: white;">
        </canvas>
        <br><br>
        <button onclick="clearCanvas()" style="padding: 10px 20px; margin: 5px; background: #ff4b4b; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Clear Canvas
        </button>
        <button onclick="getCanvasData()" style="padding: 10px 20px; margin: 5px; background: #1f77b4; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Recognize Digit
        </button>
    </div>
    
    <script>
        const canvas = document.getElementById('drawingCanvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        
        // Set up canvas
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 8;
        
        // Mouse events
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);
        
        // Touch events for mobile
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopDrawing);
        
        function startDrawing(e) {
            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            ctx.beginPath();
            ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
        }
        
        function draw(e) {
            if (!isDrawing) return;
            const rect = canvas.getBoundingClientRect();
            ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
            ctx.stroke();
        }
        
        function stopDrawing() {
            isDrawing = false;
        }
        
        function handleTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        function getCanvasData() {
            // Convert canvas to 28x28 grayscale image
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            // Create temporary canvas for resizing
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = 28;
            tempCanvas.height = 28;
            const tempCtx = tempCanvas.getContext('2d');
            
            // Draw scaled image
            tempCtx.drawImage(canvas, 0, 0, 28, 28);
            
            // Get pixel data
            const scaledImageData = tempCtx.getImageData(0, 0, 28, 28);
            const data = scaledImageData.data;
            
            // Convert to grayscale array
            const grayscale = [];
            for (let i = 0; i < data.length; i += 4) {
                const gray = (data[i] + data[i + 1] + data[i + 2]) / 3 / 255;
                grayscale.push(gray);
            }
            
            // Send data to Streamlit (this would need a proper bridge in real implementation)
            console.log('Canvas data:', grayscale);
            alert('Canvas data captured! Check console for details.');
        }
    </script>
    """
    
    return canvas_html

def main():
    """Main application"""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🧠 Neural Network Simulator</h1>', unsafe_allow_html=True)
    st.markdown("**Interactive visualization and training of neural networks with full Python implementation**")
    
    # Sidebar for configuration
    st.sidebar.title("🎛️ Configuration")
    
    # Problem type selection
    problem_type = st.sidebar.selectbox(
        "Select Problem Type",
        ["XOR Classification", "Digit Recognition", "Custom Classification", "Regression"]
    )
    
    # Network architecture
    st.sidebar.subheader("🏗️ Network Architecture")
    
    if problem_type == "XOR Classification":
        input_size = 2
        output_size = 1
        default_hidden = [4]
    elif problem_type == "Digit Recognition":
        input_size = 784  # 28x28 images
        output_size = 10  # 10 digits
        default_hidden = [128, 64]
    elif problem_type == "Custom Classification":
        input_size = st.sidebar.number_input("Input Size", min_value=1, max_value=100, value=2)
        output_size = st.sidebar.number_input("Output Size", min_value=1, max_value=20, value=2)
        default_hidden = [8, 4]
    else:  # Regression
        input_size = st.sidebar.number_input("Input Size", min_value=1, max_value=100, value=1)
        output_size = 1
        default_hidden = [10, 5]
    
    # Hidden layers configuration
    n_hidden_layers = st.sidebar.number_input("Number of Hidden Layers", min_value=1, max_value=5, value=len(default_hidden))
    
    hidden_layers = []
    for i in range(n_hidden_layers):
        default_val = default_hidden[i] if i < len(default_hidden) else 8
        size = st.sidebar.number_input(f"Hidden Layer {i+1} Size", min_value=1, max_value=512, value=default_val)
        hidden_layers.append(size)
    
    architecture = [input_size] + hidden_layers + [output_size]
    
    # Training configuration
    st.sidebar.subheader("⚙️ Training Configuration")
    
    learning_rate = st.sidebar.slider("Learning Rate", min_value=0.001, max_value=1.0, value=0.01, step=0.001)
    epochs = st.sidebar.number_input("Epochs", min_value=10, max_value=5000, value=1000)
    batch_size = st.sidebar.number_input("Batch Size", min_value=1, max_value=256, value=32)
    
    activation_function = st.sidebar.selectbox(
        "Activation Function",
        ["sigmoid", "tanh", "relu", "leaky_relu"]
    )
    
    if problem_type in ["Digit Recognition", "Custom Classification"] and output_size > 1:
        output_activation = "softmax"
    else:
        output_activation = "sigmoid"
    
    # Create training configuration
    config = TrainingConfig(
        learning_rate=learning_rate,
        epochs=epochs,
        batch_size=batch_size,
        early_stopping_patience=50,
        early_stopping_threshold=1e-6
    )
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown('<h2 class="sub-header">📊 Data & Training</h2>', unsafe_allow_html=True)
        
        # Data generation
        if st.button("🎲 Generate Data", type="primary"):
            with st.spinner("Generating data..."):
                if problem_type == "XOR Classification":
                    X, y = DataGenerator.generate_xor_data(n_samples=1000, noise=0.1)
                elif problem_type == "Digit Recognition":
                    X, y = DataGenerator.generate_synthetic_mnist(n_samples=2000)
                elif problem_type == "Custom Classification":
                    dataset_type = st.sidebar.selectbox("Dataset Type", ["random", "circles", "moons"])
                    X, y = DataGenerator.generate_classification_data(
                        n_samples=1000, 
                        n_features=input_size, 
                        n_classes=output_size,
                        dataset_type=dataset_type
                    )
                else:  # Regression
                    function_type = st.sidebar.selectbox("Function Type", ["linear", "quadratic", "sine", "complex"])
                    X, y = DataGenerator.generate_regression_data(
                        n_samples=1000,
                        n_features=input_size,
                        function_type=function_type
                    )
                
                # Split data
                split_idx = int(0.8 * len(X))
                indices = np.random.permutation(len(X))
                
                st.session_state.X_train = X[indices[:split_idx]]
                st.session_state.y_train = y[indices[:split_idx]]
                st.session_state.X_test = X[indices[split_idx:]]
                st.session_state.y_test = y[indices[split_idx:]]
                
                st.success(f"✅ Generated {len(X)} samples ({len(st.session_state.X_train)} train, {len(st.session_state.X_test)} test)")
        
        # Display data info
        if st.session_state.X_train is not None:
            st.info(f"📈 Dataset: {st.session_state.X_train.shape[0]} training samples, {st.session_state.X_train.shape[1]} features")
            
            # Visualize data (for 2D problems)
            if st.session_state.X_train.shape[1] == 2 and problem_type != "Regression":
                fig, ax = plt.subplots(figsize=(8, 6))
                
                if len(st.session_state.y_train.shape) > 1 and st.session_state.y_train.shape[1] > 1:
                    labels = np.argmax(st.session_state.y_train, axis=1)
                else:
                    labels = st.session_state.y_train.flatten()
                
                unique_labels = np.unique(labels)
                colors = plt.cm.Set1(np.linspace(0, 1, len(unique_labels)))
                
                for i, label in enumerate(unique_labels):
                    mask = labels == label
                    ax.scatter(st.session_state.X_train[mask, 0], st.session_state.X_train[mask, 1], 
                             c=[colors[i]], label=f'Class {int(label)}', alpha=0.7)
                
                ax.set_xlabel('Feature 1')
                ax.set_ylabel('Feature 2')
                ax.set_title('Training Data Distribution')
                ax.legend()
                ax.grid(True, alpha=0.3)
                
                st.pyplot(fig)
            
            # Show sample digits
            elif problem_type == "Digit Recognition":
                fig, axes = plt.subplots(2, 5, figsize=(12, 6))
                axes = axes.flatten()
                
                for digit in range(10):
                    digit_indices = np.where(np.argmax(st.session_state.y_train, axis=1) == digit)[0]
                    if len(digit_indices) > 0:
                        sample_idx = digit_indices[0]
                        image = st.session_state.X_train[sample_idx].reshape(28, 28)
                        
                        axes[digit].imshow(image, cmap='gray')
                        axes[digit].set_title(f'Digit {digit}')
                        axes[digit].axis('off')
                
                plt.tight_layout()
                st.pyplot(fig)
        
        # Model training
        if st.session_state.X_train is not None:
            if st.button("🚀 Train Model", type="primary"):
                with st.spinner("Training neural network..."):
                    # Create model
                    model = UnifiedNeuralNetwork(
                        architecture=architecture,
                        activation_function=activation_function,
                        output_activation=output_activation,
                        config=config
                    )
                    
                    # Progress bar
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    # Train model
                    start_time = time.time()
                    history = model.train(
                        st.session_state.X_train, 
                        st.session_state.y_train,
                        st.session_state.X_test,
                        st.session_state.y_test,
                        verbose=False
                    )
                    
                    training_time = time.time() - start_time
                    
                    # Update progress
                    progress_bar.progress(1.0)
                    status_text.text("Training completed!")
                    
                    # Store results
                    st.session_state.model = model
                    st.session_state.training_history = history
                    
                    # Show results
                    final_train_acc = history['accuracy'][-1]
                    final_val_acc = history['val_accuracy'][-1]
                    
                    st.success(f"✅ Training completed in {training_time:.2f} seconds!")
                    st.info(f"📊 Final Training Accuracy: {final_train_acc:.3f}")
                    st.info(f"📊 Final Validation Accuracy: {final_val_acc:.3f}")
    
    with col2:
        st.markdown('<h2 class="sub-header">🏗️ Network Architecture</h2>', unsafe_allow_html=True)
        
        # Display architecture
        arch_df = pd.DataFrame({
            'Layer': ['Input'] + [f'Hidden {i+1}' for i in range(len(hidden_layers))] + ['Output'],
            'Neurons': architecture,
            'Activation': ['Input'] + [activation_function] * len(hidden_layers) + [output_activation]
        })
        
        st.dataframe(arch_df, use_container_width=True)
        
        # Network visualization
        if st.button("🎨 Visualize Architecture"):
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # Calculate positions
            max_neurons = max(architecture)
            layer_positions = np.linspace(0, 10, len(architecture))
            
            # Draw neurons
            for layer_idx, n_neurons in enumerate(architecture):
                x = layer_positions[layer_idx]
                
                if n_neurons == 1:
                    y_positions = [max_neurons / 2]
                else:
                    y_positions = np.linspace(0, max_neurons, n_neurons)
                
                for y in y_positions:
                    circle = plt.Circle((x, y), 0.3, color='lightblue', 
                                      ec='darkblue', linewidth=2)
                    ax.add_patch(circle)
                
                # Layer labels
                layer_names = ['Input'] + [f'Hidden {i+1}' for i in range(len(hidden_layers))] + ['Output']
                ax.text(x, -1, f'{layer_names[layer_idx]}\n({n_neurons})', 
                       ha='center', va='top', fontsize=10, fontweight='bold')
            
            # Draw connections
            for layer_idx in range(len(architecture) - 1):
                x1 = layer_positions[layer_idx]
                x2 = layer_positions[layer_idx + 1]
                
                n1 = architecture[layer_idx]
                n2 = architecture[layer_idx + 1]
                
                y1_positions = np.linspace(0, max_neurons, n1) if n1 > 1 else [max_neurons / 2]
                y2_positions = np.linspace(0, max_neurons, n2) if n2 > 1 else [max_neurons / 2]
                
                for y1 in y1_positions:
                    for y2 in y2_positions:
                        ax.plot([x1 + 0.3, x2 - 0.3], [y1, y2], 
                               'gray', alpha=0.3, linewidth=0.5)
            
            ax.set_xlim(-1, 11)
            ax.set_ylim(-2, max_neurons + 1)
            ax.set_aspect('equal')
            ax.axis('off')
            ax.set_title('Neural Network Architecture', fontsize=16, fontweight='bold')
            
            st.pyplot(fig)
    
    # Training results
    if st.session_state.training_history is not None:
        st.markdown('<h2 class="sub-header">📈 Training Results</h2>', unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            final_loss = st.session_state.training_history['val_loss'][-1]
            st.metric("Final Validation Loss", f"{final_loss:.4f}")
        
        with col2:
            final_acc = st.session_state.training_history['val_accuracy'][-1]
            st.metric("Final Validation Accuracy", f"{final_acc:.3f}")
        
        with col3:
            total_epochs = len(st.session_state.training_history['loss'])
            st.metric("Total Epochs", total_epochs)
        
        # Training plots
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))
        
        epochs = range(1, len(st.session_state.training_history['loss']) + 1)
        
        # Loss plot
        axes[0].plot(epochs, st.session_state.training_history['loss'], 'b-', label='Training Loss', linewidth=2)
        axes[0].plot(epochs, st.session_state.training_history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
        axes[0].set_title('Model Loss')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Accuracy plot
        axes[1].plot(epochs, st.session_state.training_history['accuracy'], 'b-', label='Training Accuracy', linewidth=2)
        axes[1].plot(epochs, st.session_state.training_history['val_accuracy'], 'r-', label='Validation Accuracy', linewidth=2)
        axes[1].set_title('Model Accuracy')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('Accuracy')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        st.pyplot(fig)
        
        # Decision boundary (for 2D classification)
        if (st.session_state.X_train.shape[1] == 2 and 
            problem_type in ["XOR Classification", "Custom Classification"]):
            
            st.markdown('<h3 class="sub-header">🎯 Decision Boundary</h3>', unsafe_allow_html=True)
            
            # Create mesh
            x_min, x_max = st.session_state.X_train[:, 0].min() - 0.5, st.session_state.X_train[:, 0].max() + 0.5
            y_min, y_max = st.session_state.X_train[:, 1].min() - 0.5, st.session_state.X_train[:, 1].max() + 0.5
            
            xx, yy = np.meshgrid(np.linspace(x_min, x_max, 100),
                                np.linspace(y_min, y_max, 100))
            
            mesh_points = np.c_[xx.ravel(), yy.ravel()]
            Z = st.session_state.model.predict(mesh_points)
            
            if Z.shape[1] > 1:
                Z = np.argmax(Z, axis=1)
            else:
                Z = (Z > 0.5).astype(int).flatten()
            
            Z = Z.reshape(xx.shape)
            
            fig, ax = plt.subplots(figsize=(10, 8))
            ax.contourf(xx, yy, Z, alpha=0.4, cmap=plt.cm.RdYlBu)
            
            # Plot data points
            if len(st.session_state.y_train.shape) > 1 and st.session_state.y_train.shape[1] > 1:
                labels = np.argmax(st.session_state.y_train, axis=1)
            else:
                labels = st.session_state.y_train.flatten()
            
            unique_labels = np.unique(labels)
            colors = plt.cm.Set1(np.linspace(0, 1, len(unique_labels)))
            
            for i, label in enumerate(unique_labels):
                mask = labels == label
                ax.scatter(st.session_state.X_train[mask, 0], st.session_state.X_train[mask, 1], 
                         c=[colors[i]], label=f'Class {int(label)}', 
                         edgecolors='black', s=50)
            
            ax.set_xlabel('Feature 1')
            ax.set_ylabel('Feature 2')
            ax.set_title('Decision Boundary')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            st.pyplot(fig)
    
    # Digit recognition interface
    if problem_type == "Digit Recognition" and st.session_state.model is not None:
        st.markdown('<h2 class="sub-header">✏️ Draw a Digit</h2>', unsafe_allow_html=True)
        
        st.markdown("Draw a digit in the canvas below and click 'Recognize' to see the prediction:")
        
        # Note: In a real implementation, you would need a proper canvas component
        # For now, we'll show a placeholder
        st.info("🚧 Canvas drawing feature would be implemented with a proper Streamlit component or custom HTML/JS integration")
        
        # Manual digit input for testing
        st.subheader("Manual Testing")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Generate a random test digit
            if st.button("🎲 Generate Random Test Digit"):
                test_idx = np.random.randint(0, len(st.session_state.X_test))
                test_image = st.session_state.X_test[test_idx]
                true_label = np.argmax(st.session_state.y_test[test_idx])
                
                # Make prediction
                result = st.session_state.model.predict_single(test_image)
                
                # Display image
                fig, ax = plt.subplots(figsize=(4, 4))
                ax.imshow(test_image.reshape(28, 28), cmap='gray')
                ax.set_title(f'True Label: {true_label}')
                ax.axis('off')
                st.pyplot(fig)
        
        with col2:
            if 'result' in locals():
                st.subheader("Prediction Results")
                st.metric("Predicted Digit", result['predicted_class'])
                st.metric("Confidence", f"{result['confidence']:.1%}")
                
                # Show all probabilities
                prob_df = pd.DataFrame({
                    'Digit': range(10),
                    'Probability': result['probabilities']
                })
                
                fig, ax = plt.subplots(figsize=(8, 4))
                bars = ax.bar(prob_df['Digit'], prob_df['Probability'])
                
                # Highlight predicted digit
                bars[result['predicted_class']].set_color('red')
                
                ax.set_xlabel('Digit')
                ax.set_ylabel('Probability')
                ax.set_title('Prediction Probabilities')
                ax.grid(True, alpha=0.3)
                
                st.pyplot(fig)

if __name__ == "__main__":
    main()
