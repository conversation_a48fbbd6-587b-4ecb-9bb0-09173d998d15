import numpy as np
import json
from typing import List, Tuple
import pickle
import os

class DigitNeuralNetwork:
    """
    Neural Network for digit recognition (0-9)
    Architecture: 784 -> 128 -> 64 -> 10
    """
    
    def __init__(self, input_size=784, hidden1_size=128, hidden2_size=64, output_size=10, learning_rate=0.01):
        self.input_size = input_size
        self.hidden1_size = hidden1_size
        self.hidden2_size = hidden2_size
        self.output_size = output_size
        self.learning_rate = learning_rate
        
        # Initialize weights and biases
        self.initialize_weights()
        
        # Training history
        self.training_history = []
        self.is_trained = False
        
    def initialize_weights(self):
        """Initialize weights using Xavier initialization"""
        # Input to hidden1
        self.W1 = np.random.randn(self.input_size, self.hidden1_size) * np.sqrt(2.0 / self.input_size)
        self.b1 = np.zeros((1, self.hidden1_size))
        
        # Hidden1 to hidden2
        self.W2 = np.random.randn(self.hidden1_size, self.hidden2_size) * np.sqrt(2.0 / self.hidden1_size)
        self.b2 = np.zeros((1, self.hidden2_size))
        
        # Hidden2 to output
        self.W3 = np.random.randn(self.hidden2_size, self.output_size) * np.sqrt(2.0 / self.hidden2_size)
        self.b3 = np.zeros((1, self.output_size))
    
    def relu(self, x):
        """ReLU activation function"""
        return np.maximum(0, x)
    
    def relu_derivative(self, x):
        """ReLU derivative"""
        return (x > 0).astype(float)
    
    def softmax(self, x):
        """Softmax activation function"""
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)
    
    def forward(self, X):
        """Forward propagation"""
        # Input to hidden1
        self.z1 = np.dot(X, self.W1) + self.b1
        self.a1 = self.relu(self.z1)
        
        # Hidden1 to hidden2
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        self.a2 = self.relu(self.z2)
        
        # Hidden2 to output
        self.z3 = np.dot(self.a2, self.W3) + self.b3
        self.a3 = self.softmax(self.z3)
        
        return self.a3
    
    def backward(self, X, y, output):
        """Backward propagation"""
        m = X.shape[0]
        
        # Output layer gradients
        dz3 = output - y
        dW3 = (1/m) * np.dot(self.a2.T, dz3)
        db3 = (1/m) * np.sum(dz3, axis=0, keepdims=True)
        
        # Hidden2 layer gradients
        da2 = np.dot(dz3, self.W3.T)
        dz2 = da2 * self.relu_derivative(self.z2)
        dW2 = (1/m) * np.dot(self.a1.T, dz2)
        db2 = (1/m) * np.sum(dz2, axis=0, keepdims=True)
        
        # Hidden1 layer gradients
        da1 = np.dot(dz2, self.W2.T)
        dz1 = da1 * self.relu_derivative(self.z1)
        dW1 = (1/m) * np.dot(X.T, dz1)
        db1 = (1/m) * np.sum(dz1, axis=0, keepdims=True)
        
        # Update weights and biases
        self.W3 -= self.learning_rate * dW3
        self.b3 -= self.learning_rate * db3
        self.W2 -= self.learning_rate * dW2
        self.b2 -= self.learning_rate * db2
        self.W1 -= self.learning_rate * dW1
        self.b1 -= self.learning_rate * db1
    
    def train(self, X_train, y_train, X_test=None, y_test=None, epochs=100, batch_size=32):
        """Train the neural network"""
        print(f"Training neural network for {epochs} epochs...")
        
        for epoch in range(epochs):
            # Shuffle training data
            indices = np.random.permutation(X_train.shape[0])
            X_train_shuffled = X_train[indices]
            y_train_shuffled = y_train[indices]
            
            # Mini-batch training
            for i in range(0, X_train.shape[0], batch_size):
                X_batch = X_train_shuffled[i:i+batch_size]
                y_batch = y_train_shuffled[i:i+batch_size]
                
                # Forward and backward pass
                output = self.forward(X_batch)
                self.backward(X_batch, y_batch, output)
            
            # Calculate accuracy every 10 epochs
            if epoch % 10 == 0:
                train_accuracy = self.calculate_accuracy(X_train, y_train)
                test_accuracy = self.calculate_accuracy(X_test, y_test) if X_test is not None else 0
                
                print(f"Epoch {epoch}: Train Acc: {train_accuracy:.3f}, Test Acc: {test_accuracy:.3f}")
                
                self.training_history.append({
                    'epoch': epoch,
                    'train_accuracy': train_accuracy,
                    'test_accuracy': test_accuracy
                })
        
        self.is_trained = True
        final_accuracy = self.calculate_accuracy(X_test, y_test) if X_test is not None else self.calculate_accuracy(X_train, y_train)
        print(f"Training completed! Final accuracy: {final_accuracy:.3f}")
        
        return final_accuracy
    
    def predict(self, X):
        """Make predictions"""
        if not self.is_trained:
            print("Warning: Model not trained yet!")
        
        output = self.forward(X)
        return output
    
    def predict_single(self, image_data):
        """Predict single image"""
        # Reshape and normalize
        X = np.array(image_data).reshape(1, -1)
        
        # Get prediction probabilities
        probabilities = self.predict(X)[0]
        
        # Get predicted digit
        predicted_digit = np.argmax(probabilities)
        confidence = probabilities[predicted_digit]
        
        return {
            'predicted_digit': int(predicted_digit),
            'confidence': float(confidence),
            'all_predictions': probabilities.tolist()
        }
    
    def calculate_accuracy(self, X, y):
        """Calculate accuracy"""
        predictions = self.predict(X)
        predicted_labels = np.argmax(predictions, axis=1)
        true_labels = np.argmax(y, axis=1)
        accuracy = np.mean(predicted_labels == true_labels)
        return accuracy
    
    def save_model(self, filepath):
        """Save model to file"""
        model_data = {
            'W1': self.W1,
            'b1': self.b1,
            'W2': self.W2,
            'b2': self.b2,
            'W3': self.W3,
            'b3': self.b3,
            'input_size': self.input_size,
            'hidden1_size': self.hidden1_size,
            'hidden2_size': self.hidden2_size,
            'output_size': self.output_size,
            'learning_rate': self.learning_rate,
            'is_trained': self.is_trained,
            'training_history': self.training_history
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"Model saved to {filepath}")
    
    def load_model(self, filepath):
        """Load model from file"""
        if not os.path.exists(filepath):
            print(f"Model file {filepath} not found!")
            return False
        
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.W1 = model_data['W1']
            self.b1 = model_data['b1']
            self.W2 = model_data['W2']
            self.b2 = model_data['b2']
            self.W3 = model_data['W3']
            self.b3 = model_data['b3']
            self.input_size = model_data['input_size']
            self.hidden1_size = model_data['hidden1_size']
            self.hidden2_size = model_data['hidden2_size']
            self.output_size = model_data['output_size']
            self.learning_rate = model_data['learning_rate']
            self.is_trained = model_data['is_trained']
            self.training_history = model_data['training_history']
            
            print(f"Model loaded from {filepath}")
            return True
            
        except Exception as e:
            print(f"Error loading model: {e}")
            return False

def create_synthetic_mnist_data(num_samples=1000):
    """
    Create synthetic MNIST-like data for demonstration
    In a real application, you would load the actual MNIST dataset
    """
    print("Creating synthetic training data...")
    
    X_train = []
    y_train = []
    
    for digit in range(10):
        for _ in range(num_samples // 10):
            # Create a simple pattern for each digit
            image = np.zeros((28, 28))
            
            if digit == 0:  # Circle
                center = (14, 14)
                for i in range(28):
                    for j in range(28):
                        dist = np.sqrt((i - center[0])**2 + (j - center[1])**2)
                        if 8 < dist < 12:
                            image[i, j] = 1
            
            elif digit == 1:  # Vertical line
                image[5:23, 12:16] = 1
            
            elif digit == 2:  # Horizontal lines
                image[8:12, 5:23] = 1
                image[16:20, 5:23] = 1
            
            # Add more patterns for other digits...
            # This is simplified - real MNIST has much more complex patterns
            
            # Add noise
            noise = np.random.normal(0, 0.1, (28, 28))
            image = np.clip(image + noise, 0, 1)
            
            X_train.append(image.flatten())
            
            # One-hot encoding
            label = np.zeros(10)
            label[digit] = 1
            y_train.append(label)
    
    return np.array(X_train), np.array(y_train)

if __name__ == "__main__":
    # Test the neural network
    print("Testing Digit Neural Network...")
    
    # Create synthetic data
    X_train, y_train = create_synthetic_mnist_data(1000)
    X_test, y_test = create_synthetic_mnist_data(200)
    
    # Create and train model
    model = DigitNeuralNetwork()
    accuracy = model.train(X_train, y_train, X_test, y_test, epochs=50)
    
    # Save model
    model.save_model('digit_model.pkl')
    
    # Test single prediction
    test_image = X_test[0]
    result = model.predict_single(test_image)
    print(f"Test prediction: {result}")
