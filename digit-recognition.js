class DigitRecognition {
    constructor() {
        this.canvas = document.getElementById('drawing-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.isDrawing = false;
        this.lastX = 0;
        this.lastY = 0;
        
        this.setupCanvas();
        this.setupEventListeners();
        this.initializeConfidenceChart();
        this.checkModelStatus();
    }
    
    setupCanvas() {
        // Set up canvas for drawing
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        this.ctx.strokeStyle = '#000';
        this.ctx.lineWidth = 8;
        
        // Clear canvas
        this.clearCanvas();
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousedown', this.startDrawing.bind(this));
        this.canvas.addEventListener('mousemove', this.draw.bind(this));
        this.canvas.addEventListener('mouseup', this.stopDrawing.bind(this));
        this.canvas.addEventListener('mouseout', this.stopDrawing.bind(this));
        
        // Touch events for mobile
        this.canvas.addEventListener('touchstart', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchmove', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchend', this.stopDrawing.bind(this));
        
        // Button events
        document.getElementById('clear-canvas').addEventListener('click', this.clearCanvas.bind(this));
        document.getElementById('predict-digit').addEventListener('click', this.predictDigit.bind(this));
        document.getElementById('train-digit-model').addEventListener('click', this.trainModel.bind(this));
    }
    
    startDrawing(e) {
        this.isDrawing = true;
        const rect = this.canvas.getBoundingClientRect();
        this.lastX = e.clientX - rect.left;
        this.lastY = e.clientY - rect.top;
    }
    
    draw(e) {
        if (!this.isDrawing) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const currentX = e.clientX - rect.left;
        const currentY = e.clientY - rect.top;
        
        this.ctx.beginPath();
        this.ctx.moveTo(this.lastX, this.lastY);
        this.ctx.lineTo(currentX, currentY);
        this.ctx.stroke();
        
        this.lastX = currentX;
        this.lastY = currentY;
    }
    
    stopDrawing() {
        this.isDrawing = false;
    }
    
    handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                        e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        this.canvas.dispatchEvent(mouseEvent);
    }
    
    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Add subtle grid for better drawing guidance
        this.ctx.strokeStyle = '#f0f0f0';
        this.ctx.lineWidth = 1;
        
        // Draw grid lines
        for (let i = 0; i <= this.canvas.width; i += 28) {
            this.ctx.beginPath();
            this.ctx.moveTo(i, 0);
            this.ctx.lineTo(i, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let i = 0; i <= this.canvas.height; i += 28) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, i);
            this.ctx.lineTo(this.canvas.width, i);
            this.ctx.stroke();
        }
        
        // Reset drawing style
        this.ctx.strokeStyle = '#000';
        this.ctx.lineWidth = 8;
        
        // Clear predictions
        this.clearPredictions();
    }
    
    clearPredictions() {
        document.getElementById('predicted-digit').textContent = '-';
        document.getElementById('confidence-score').textContent = '-';
        this.updateConfidenceChart([]);
    }
    
    async predictDigit() {
        try {
            // Get image data from canvas
            const imageData = this.getCanvasImageData();
            
            // Show loading state
            document.getElementById('predicted-digit').textContent = '...';
            document.getElementById('confidence-score').textContent = 'Processing...';
            
            // Send to Python backend
            const response = await fetch('/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image_data: imageData
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            // Update UI with results
            this.displayPrediction(result);
            
        } catch (error) {
            console.error('Prediction error:', error);
            this.showError('Failed to predict digit. Make sure the Python server is running.');
        }
    }
    
    getCanvasImageData() {
        // Convert canvas to 28x28 grayscale image (MNIST format)
        const canvas28 = document.createElement('canvas');
        canvas28.width = 28;
        canvas28.height = 28;
        const ctx28 = canvas28.getContext('2d');
        
        // Scale down the drawing
        ctx28.drawImage(this.canvas, 0, 0, 28, 28);
        
        // Get image data
        const imageData = ctx28.getImageData(0, 0, 28, 28);
        const data = imageData.data;
        
        // Convert to grayscale array (0-1 range)
        const grayscale = [];
        for (let i = 0; i < data.length; i += 4) {
            // Average RGB values and normalize
            const gray = (data[i] + data[i + 1] + data[i + 2]) / 3 / 255;
            grayscale.push(gray);
        }
        
        return grayscale;
    }
    
    displayPrediction(result) {
        // Display predicted digit
        document.getElementById('predicted-digit').textContent = result.predicted_digit;
        
        // Display confidence
        const confidence = (result.confidence * 100).toFixed(1);
        document.getElementById('confidence-score').textContent = `${confidence}%`;
        
        // Update confidence chart
        this.updateConfidenceChart(result.all_predictions);
    }
    
    updateConfidenceChart(predictions) {
        const chartContainer = document.getElementById('confidence-chart');
        chartContainer.innerHTML = '';
        
        if (!predictions || predictions.length === 0) {
            chartContainer.innerHTML = '<p style="text-align: center; color: #718096;">No predictions available</p>';
            return;
        }
        
        // Create bars for each digit (0-9)
        for (let i = 0; i < 10; i++) {
            const confidence = predictions[i] || 0;
            const percentage = (confidence * 100).toFixed(1);
            
            const barElement = document.createElement('div');
            barElement.className = 'confidence-bar';
            
            barElement.innerHTML = `
                <span class="bar-label">${i}</span>
                <div class="bar-container">
                    <div class="bar-fill" style="width: ${percentage}%"></div>
                </div>
                <span class="bar-value">${percentage}%</span>
            `;
            
            chartContainer.appendChild(barElement);
        }
    }
    
    initializeConfidenceChart() {
        // Initialize empty chart
        this.updateConfidenceChart([]);
    }
    
    async trainModel() {
        try {
            document.getElementById('model-status').textContent = 'Training...';
            
            const response = await fetch('/train', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            // Update model status
            document.getElementById('model-status').textContent = 'Trained';
            document.getElementById('model-accuracy').textContent = `${(result.accuracy * 100).toFixed(1)}%`;
            
            this.showSuccess(`Model trained successfully! Accuracy: ${(result.accuracy * 100).toFixed(1)}%`);
            
        } catch (error) {
            console.error('Training error:', error);
            document.getElementById('model-status').textContent = 'Training Failed';
            this.showError('Failed to train model. Make sure the Python server is running.');
        }
    }
    
    async checkModelStatus() {
        try {
            const response = await fetch('/status');
            if (response.ok) {
                const result = await response.json();
                document.getElementById('model-status').textContent = result.status;
                if (result.accuracy) {
                    document.getElementById('model-accuracy').textContent = `${(result.accuracy * 100).toFixed(1)}%`;
                }
            } else {
                document.getElementById('model-status').textContent = 'Server Offline';
            }
        } catch (error) {
            document.getElementById('model-status').textContent = 'Server Offline';
        }
    }
    
    showError(message) {
        this.showToast(message, 'error');
    }
    
    showSuccess(message) {
        this.showToast(message, 'success');
    }
    
    showToast(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        `;
        
        switch (type) {
            case 'success':
                toast.style.backgroundColor = '#48bb78';
                break;
            case 'error':
                toast.style.backgroundColor = '#f56565';
                break;
            case 'info':
            default:
                toast.style.backgroundColor = '#667eea';
                break;
        }
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
}
