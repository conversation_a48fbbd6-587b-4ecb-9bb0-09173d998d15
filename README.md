# 🧠 Neural Network Visualization Simulator

Simulasi interaktif yang menampilkan cara kerja neural network dengan visualisasi yang menarik dan mudah dipahami.

## ✨ Fitur Utama

### 🎯 Visualisasi Real-time
- **Arsitektur Network**: Visualisasi nodes dan koneksi dengan animasi
- **Aktivasi Neuron**: Warna dan ukuran berubah sesuai tingkat aktivasi
- **Signal Flow**: Animasi aliran data saat training
- **Weight Visualization**: <PERSON><PERSON><PERSON><PERSON> dan warna garis menunjukkan bobot koneksi

### 🎛️ Kontrol Interaktif
- **Learning Rate**: Atur kecepatan pembelajaran (0.01 - 1.0)
- **Hidden Neurons**: Ubah jumlah neuron tersembunyi (2-8)
- **Activation Function**: Pilih fungsi aktivasi (Sig<PERSON><PERSON>, <PERSON><PERSON>, ReLU)
- **Real-time Training**: Start/pause training dengan visualisasi langsung

### 📊 Monitoring & Analytics
- **Loss Chart**: <PERSON>ik pen<PERSON> error selama training
- **Accuracy Tracking**: Persentase akurasi prediksi
- **Epoch Counter**: Jumlah iterasi training
- **Dataset Visualization**: Tampilan visual problem XOR

### 🧪 Testing & Prediction
- **Interactive Testing**: Input nilai X,Y untuk prediksi
- **Accuracy Calculation**: Evaluasi performa network
- **Real-time Feedback**: Hasil prediksi langsung

### ✏️ **FITUR BARU: Digit Recognition**
- **Drawing Canvas**: Gambar angka 0-9 dengan mouse/touch
- **Python Neural Network**: Backend dengan Flask API
- **Real-time Recognition**: Prediksi langsung setelah menggambar
- **Confidence Visualization**: Bar chart untuk semua prediksi (0-9)
- **Model Training**: Train neural network dengan synthetic MNIST data
- **Deep Architecture**: 784 → 128 → 64 → 10 neurons

## 🚀 Cara Menggunakan

### 1. Setup & Instalasi

#### Untuk XOR Neural Network (JavaScript Only):
```bash
# Buka file index.html di browser langsung
# Atau gunakan local server
python -m http.server 8000
# Kemudian buka http://localhost:8000
```

#### Untuk Digit Recognition (Python + JavaScript):
```bash
# 1. Install Python dependencies
pip install -r requirements.txt

# 2. Jalankan Flask server
python app.py

# 3. Buka browser ke http://localhost:5000
```

#### Atau gunakan script otomatis (Windows):
```bash
# Double-click run_server.bat
# Atau jalankan dari command prompt:
run_server.bat
```

### 2. Mulai Training
1. **Atur Parameter**:
   - Learning Rate: Mulai dengan 0.1
   - Hidden Neurons: Coba 4-6 neuron
   - Activation: Sigmoid untuk pemula

2. **Klik "🚀 Start Training"**:
   - Network akan mulai belajar XOR problem
   - Lihat animasi signal flow
   - Monitor penurunan loss di chart

3. **Observasi Visualisasi**:
   - Neuron aktif akan berwarna hijau
   - Koneksi kuat ditampilkan lebih tebal
   - Loss chart menunjukkan progress learning

### 3. Test Network
1. **Klik "🧪 Test Network"** untuk evaluasi
2. **Manual Prediction**:
   - Input nilai X dan Y (0-1)
   - Klik "Predict" untuk hasil
   - Bandingkan dengan expected output

### 4. Digit Recognition
1. **Train Model Python**:
   - Klik "🚀 Train Model" untuk training neural network
   - Tunggu hingga training selesai (1-2 menit)
   - Monitor accuracy di status panel

2. **Draw & Recognize**:
   - Gambar angka 0-9 di canvas dengan mouse/touch
   - Klik "🔍 Recognize" untuk prediksi
   - Lihat confidence score untuk semua digit

3. **Analyze Results**:
   - Bar chart menunjukkan probabilitas setiap digit
   - Predicted digit dengan confidence tertinggi
   - Model status dan accuracy information

### 5. Eksperimen
- **Reset Network**: Mulai dari awal dengan bobot random
- **Ubah Architecture**: Coba jumlah neuron berbeda
- **Ganti Activation**: Bandingkan performa fungsi aktivasi
- **Compare Models**: XOR vs Digit Recognition performance

## 🧮 Implementasi Neural Network

### Arsitektur
```
Input Layer (2) → Hidden Layer (4) → Output Layer (1)
```

### Algoritma
1. **Forward Propagation**:
   ```javascript
   hidden = activate(input × weights_ih + bias_h)
   output = activate(hidden × weights_ho + bias_o)
   ```

2. **Backpropagation**:
   ```javascript
   error_output = (target - output) × derivative(output)
   error_hidden = error_output × weights_ho × derivative(hidden)
   ```

3. **Weight Update**:
   ```javascript
   weights += learning_rate × error × activation
   ```

### XOR Problem
Dataset yang digunakan untuk training:
- [0,0] → 0
- [0,1] → 1  
- [1,0] → 1
- [1,1] → 0

## 🎨 Teknologi

- **HTML5 Canvas**: Rendering visualisasi
- **Vanilla JavaScript**: Implementasi neural network
- **CSS3**: Styling dan animasi
- **No Dependencies**: Tidak memerlukan library eksternal

## 📚 Konsep yang Dipelajari

### 1. Neural Network Basics
- Struktur layer dan neuron
- Forward dan backward propagation
- Gradient descent optimization

### 2. Activation Functions
- **Sigmoid**: Smooth, output 0-1
- **Tanh**: Output -1 to 1
- **ReLU**: Simple, fast computation

### 3. Training Process
- Loss function (Mean Squared Error)
- Learning rate impact
- Convergence behavior

### 4. Hyperparameter Tuning
- Learning rate selection
- Network architecture design
- Activation function choice

## 🎯 Tips Penggunaan

### Untuk Pemula
1. Mulai dengan default settings
2. Amati bagaimana loss turun
3. Coba ubah learning rate dan lihat efeknya
4. Test network setelah training selesai

### Untuk Advanced
1. Eksperimen dengan architecture berbeda
2. Bandingkan performa activation functions
3. Analisis convergence speed
4. Coba input values di luar training data

## 🔧 Troubleshooting

### XOR Network Issues
- **Network Tidak Converge**: Turunkan learning rate, tambah hidden neurons
- **Training Terlalu Lambat**: Naikkan learning rate, gunakan ReLU activation
- **Accuracy Rendah**: Training lebih lama, adjust learning rate

### Digit Recognition Issues
- **"Failed to train model"**: Pastikan Python server berjalan di port 5000
- **Server tidak start**: Install dependencies dengan `pip install -r requirements.txt`
- **Model accuracy rendah**: Normal untuk synthetic data (~40-60%)
- **Prediction tidak akurat**: Gambar angka dengan jelas di tengah canvas

### Common Solutions
- **Refresh browser** jika ada masalah loading
- **Restart server** dengan Ctrl+C dan jalankan ulang
- **Check console** (F12) untuk error messages
- **Clear canvas** sebelum menggambar angka baru

## 🎓 Pembelajaran Lanjutan

Setelah memahami simulasi ini, Anda bisa:
1. Implementasi dataset yang lebih complex
2. Tambah multiple hidden layers
3. Implementasi different loss functions
4. Eksplorasi optimization algorithms (Adam, RMSprop)
5. Visualisasi decision boundaries

## 📝 Lisensi

Open source - bebas digunakan untuk pembelajaran dan pengembangan.

---

**Selamat belajar Neural Networks! 🚀🧠**
