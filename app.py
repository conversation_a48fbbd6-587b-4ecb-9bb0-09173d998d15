from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import numpy as np
import os
import threading
import time
from digit_model import DigitNeuralNetwork, create_synthetic_mnist_data

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Global model instance
model = None
model_status = "Not Loaded"
model_accuracy = 0.0
training_in_progress = False

def initialize_model():
    """Initialize the neural network model"""
    global model, model_status, model_accuracy
    
    model = DigitNeuralNetwork()
    
    # Try to load existing model
    if os.path.exists('digit_model.pkl'):
        if model.load_model('digit_model.pkl'):
            model_status = "Loaded"
            # Calculate accuracy from training history
            if model.training_history:
                model_accuracy = model.training_history[-1].get('test_accuracy', 0.0)
            print("Model loaded successfully!")
        else:
            model_status = "Load Failed"
    else:
        model_status = "Not Trained"
        print("No pre-trained model found. Please train the model first.")

@app.route('/')
def index():
    """Serve the main HTML file"""
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('.', filename)

@app.route('/status', methods=['GET'])
def get_status():
    """Get model status"""
    global model_status, model_accuracy, training_in_progress
    
    status = model_status
    if training_in_progress:
        status = "Training..."
    
    return jsonify({
        'status': status,
        'accuracy': model_accuracy,
        'is_trained': model.is_trained if model else False,
        'training_in_progress': training_in_progress
    })

@app.route('/predict', methods=['POST'])
def predict_digit():
    """Predict digit from image data"""
    global model, model_status
    
    try:
        if not model or not model.is_trained:
            return jsonify({
                'error': 'Model not trained. Please train the model first.',
                'predicted_digit': -1,
                'confidence': 0.0,
                'all_predictions': [0.0] * 10
            }), 400
        
        data = request.get_json()
        image_data = data.get('image_data', [])
        
        if len(image_data) != 784:  # 28x28 = 784
            return jsonify({
                'error': 'Invalid image data. Expected 784 pixels.',
                'predicted_digit': -1,
                'confidence': 0.0,
                'all_predictions': [0.0] * 10
            }), 400
        
        # Make prediction
        result = model.predict_single(image_data)
        
        return jsonify(result)
        
    except Exception as e:
        print(f"Prediction error: {e}")
        return jsonify({
            'error': str(e),
            'predicted_digit': -1,
            'confidence': 0.0,
            'all_predictions': [0.0] * 10
        }), 500

def train_model_background():
    """Train model in background thread"""
    global model, model_status, model_accuracy, training_in_progress
    
    try:
        training_in_progress = True
        model_status = "Training..."
        
        print("Starting model training...")
        
        # Create synthetic training data
        print("Generating training data...")
        X_train, y_train = create_synthetic_mnist_data(2000)  # More data for better training
        X_test, y_test = create_synthetic_mnist_data(400)
        
        print("Training neural network...")
        # Train the model
        accuracy = model.train(X_train, y_train, X_test, y_test, epochs=100, batch_size=64)
        
        # Save the trained model
        model.save_model('digit_model.pkl')
        
        # Update global status
        model_accuracy = accuracy
        model_status = "Trained"
        training_in_progress = False
        
        print(f"Training completed! Accuracy: {accuracy:.3f}")
        
    except Exception as e:
        print(f"Training error: {e}")
        model_status = "Training Failed"
        training_in_progress = False

@app.route('/train', methods=['POST'])
def train_model():
    """Start model training"""
    global model, training_in_progress
    
    if training_in_progress:
        return jsonify({
            'error': 'Training already in progress',
            'accuracy': 0.0
        }), 400
    
    if not model:
        initialize_model()
    
    # Start training in background thread
    training_thread = threading.Thread(target=train_model_background)
    training_thread.daemon = True
    training_thread.start()
    
    return jsonify({
        'message': 'Training started',
        'status': 'Training...'
    })

@app.route('/training_progress', methods=['GET'])
def get_training_progress():
    """Get training progress"""
    global model, training_in_progress
    
    if not model:
        return jsonify({
            'training_in_progress': False,
            'history': []
        })
    
    return jsonify({
        'training_in_progress': training_in_progress,
        'history': model.training_history,
        'current_accuracy': model_accuracy
    })

@app.route('/reset_model', methods=['POST'])
def reset_model():
    """Reset the model"""
    global model, model_status, model_accuracy, training_in_progress
    
    if training_in_progress:
        return jsonify({
            'error': 'Cannot reset while training is in progress'
        }), 400
    
    # Initialize new model
    model = DigitNeuralNetwork()
    model_status = "Not Trained"
    model_accuracy = 0.0
    
    # Remove saved model file
    if os.path.exists('digit_model.pkl'):
        os.remove('digit_model.pkl')
    
    return jsonify({
        'message': 'Model reset successfully',
        'status': model_status
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🧠 Neural Network Digit Recognition Server")
    print("=" * 50)
    
    # Initialize model
    initialize_model()
    
    print(f"Model Status: {model_status}")
    print("Starting Flask server...")
    print("Access the application at: http://localhost:5000")
    print("=" * 50)
    
    # Run Flask app
    app.run(host='0.0.0.0', port=5000, debug=True, threaded=True)
