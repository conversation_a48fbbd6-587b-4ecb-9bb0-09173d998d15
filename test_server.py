#!/usr/bin/env python3
"""
Simple test script to check if the Flask server is working
"""

import requests
import json
import numpy as np

def test_server():
    """Test if the Flask server is responding"""
    base_url = "http://localhost:5000"
    
    print("🧠 Testing Neural Network Server")
    print("=" * 40)
    
    try:
        # Test status endpoint
        print("1. Testing server status...")
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            status_data = response.json()
            print(f"   ✅ Server is running!")
            print(f"   📊 Model Status: {status_data['status']}")
            print(f"   🎯 Accuracy: {status_data.get('accuracy', 'N/A')}")
        else:
            print(f"   ❌ Server error: {response.status_code}")
            return False
        
        # Test prediction endpoint with dummy data
        print("\n2. Testing prediction endpoint...")
        dummy_image = np.random.rand(784).tolist()  # Random 28x28 image
        
        prediction_data = {
            "image_data": dummy_image
        }
        
        response = requests.post(f"{base_url}/predict", 
                               json=prediction_data,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Prediction successful!")
            print(f"   🔢 Predicted digit: {result['predicted_digit']}")
            print(f"   📈 Confidence: {result['confidence']:.1%}")
        else:
            print(f"   ❌ Prediction failed: {response.status_code}")
            print(f"   📝 Response: {response.text}")
        
        print("\n🎉 Server test completed!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server!")
        print("💡 Make sure the Flask server is running on http://localhost:5000")
        print("   Run: python app.py")
        return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_server()
