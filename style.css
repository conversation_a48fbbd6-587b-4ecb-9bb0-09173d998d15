* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 300px 1fr 400px;
    gap: 20px;
    margin-bottom: 30px;
}

/* Control Panel */
.control-panel {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    height: fit-content;
}

.control-panel h3 {
    margin-bottom: 20px;
    color: #4a5568;
    font-weight: 600;
}

.control-group {
    margin-bottom: 20px;
}

.control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2d3748;
}

.control-group input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
}

.control-group span {
    font-weight: 600;
    color: #667eea;
}

.control-group select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
}

.button-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 25px 0;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn.secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn.tertiary {
    background: #48bb78;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.stats {
    border-top: 2px solid #e2e8f0;
    padding-top: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.stat-label {
    font-weight: 500;
    color: #4a5568;
}

/* Network Container */
.network-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.network-container h3 {
    margin-bottom: 20px;
    color: #4a5568;
    font-weight: 600;
}

#network-canvas {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    display: block;
    margin: 0 auto;
    background: #f8fafc;
}

.network-info {
    display: flex;
    justify-content: space-around;
    margin-top: 15px;
}

.layer-info {
    text-align: center;
}

.layer-label {
    display: block;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
}

.layer-desc {
    font-size: 12px;
    color: #718096;
}

/* Chart Container */
.chart-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin-bottom: 20px;
    color: #4a5568;
    font-weight: 600;
}

#loss-chart {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    display: block;
    margin: 0 auto;
    background: #f8fafc;
}

/* Dataset Section */
.dataset-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.dataset-section h3 {
    margin-bottom: 20px;
    color: #4a5568;
    font-weight: 600;
}

.dataset-container {
    display: flex;
    gap: 30px;
    align-items: center;
}

#dataset-canvas {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: #f8fafc;
}

.dataset-info h4 {
    margin-bottom: 15px;
    color: #2d3748;
    font-weight: 600;
}

.data-point {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-family: 'Courier New', monospace;
}

.input {
    background: #e2e8f0;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: 600;
}

.arrow {
    margin: 0 10px;
    color: #667eea;
    font-weight: bold;
}

.output {
    background: #48bb78;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: 600;
}

/* Test Section */
.test-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.test-section h3 {
    margin-bottom: 20px;
    color: #4a5568;
    font-weight: 600;
}

.test-inputs {
    display: flex;
    gap: 15px;
    align-items: end;
    margin-bottom: 15px;
}

.input-group {
    flex: 1;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2d3748;
}

.input-group input {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
}

.prediction-result {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

#prediction-value {
    color: #667eea;
    font-size: 20px;
}

/* Digit Recognition Section */
.digit-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.digit-section h3 {
    margin-bottom: 20px;
    color: #4a5568;
    font-weight: 600;
}

.digit-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

.drawing-area {
    text-align: center;
}

.drawing-area h4 {
    margin-bottom: 15px;
    color: #2d3748;
    font-weight: 600;
}

#drawing-canvas {
    border: 3px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    cursor: crosshair;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 15px;
}

#drawing-canvas:hover {
    border-color: #667eea;
}

.drawing-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 15px;
}

.drawing-info {
    font-size: 14px;
    color: #718096;
    line-height: 1.5;
}

.drawing-info p {
    margin: 5px 0;
}

.prediction-area h4 {
    margin-bottom: 20px;
    color: #2d3748;
    font-weight: 600;
}

.digit-prediction {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.predicted-digit {
    margin-bottom: 15px;
}

.digit-label, .confidence-label {
    font-weight: 500;
    color: #4a5568;
    margin-right: 10px;
}

.digit-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    background: white;
    padding: 10px 20px;
    border-radius: 10px;
    display: inline-block;
    min-width: 80px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.confidence-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #48bb78;
}

.confidence-bars h5 {
    margin-bottom: 15px;
    color: #2d3748;
    font-weight: 600;
}

.confidence-chart {
    background: #f7fafc;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.confidence-bar {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.confidence-bar:last-child {
    margin-bottom: 0;
}

.bar-label {
    width: 20px;
    font-weight: 600;
    color: #2d3748;
}

.bar-container {
    flex: 1;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    margin: 0 10px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.bar-value {
    width: 50px;
    text-align: right;
    font-weight: 500;
    color: #4a5568;
}

.model-status {
    border-top: 2px solid #e2e8f0;
    padding-top: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.status-label {
    font-weight: 500;
    color: #4a5568;
}

.status-value {
    font-weight: 600;
    color: #667eea;
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.neuron-active {
    animation: pulse 0.5s ease-in-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .dataset-container {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .test-inputs {
        flex-direction: column;
        align-items: stretch;
    }
}
