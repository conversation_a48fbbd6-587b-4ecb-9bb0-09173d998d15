#!/usr/bin/env python3
"""
Simple demo script for Full Python Neural Network Implementation
Tests XOR and Digit Recognition without external dependencies
"""

import numpy as np
import time
import sys
import os

# Import our modules
from neural_network_core import UnifiedNeuralNetwork, TrainingConfig
from data_generators import DataGenerator

def test_xor_network():
    """Test XOR classification"""
    print("🧠 Testing XOR Neural Network")
    print("=" * 50)
    
    # Generate XOR data
    print("📊 Generating XOR dataset...")
    X, y = DataGenerator.generate_xor_data(n_samples=1000, noise=0.1)
    
    # Split data
    split_idx = int(0.8 * len(X))
    indices = np.random.permutation(len(X))
    
    X_train = X[indices[:split_idx]]
    y_train = y[indices[:split_idx]]
    X_test = X[indices[split_idx:]]
    y_test = y[indices[split_idx:]]
    
    print(f"📈 Dataset: {len(X_train)} train, {len(X_test)} test samples")
    
    # Create network
    config = TrainingConfig(
        learning_rate=0.1,
        epochs=500,
        batch_size=32,
        early_stopping_patience=50
    )
    
    model = UnifiedNeuralNetwork(
        architecture=[2, 8, 4, 1],
        activation_function='sigmoid',
        output_activation='sigmoid',
        config=config
    )
    
    print(f"🏗️ Network Architecture: {model.architecture}")
    
    # Train model
    print("\n🚀 Training network...")
    start_time = time.time()
    
    history = model.train(X_train, y_train, X_test, y_test, verbose=True)
    
    training_time = time.time() - start_time
    print(f"\n⏱️ Training completed in {training_time:.2f} seconds")
    
    # Test predictions
    print("\n🧪 Testing predictions...")
    test_cases = [
        [0, 0],  # Expected: 0
        [0, 1],  # Expected: 1
        [1, 0],  # Expected: 1
        [1, 1]   # Expected: 0
    ]
    
    for inputs in test_cases:
        result = model.predict_single(np.array(inputs))
        predicted = result['predicted_class']
        confidence = result['confidence']
        expected = inputs[0] ^ inputs[1]  # XOR logic
        
        status = "✅" if predicted == expected else "❌"
        print(f"{status} Input: {inputs} → Predicted: {predicted} (confidence: {confidence:.1%}), Expected: {expected}")
    
    # Calculate final accuracy
    final_accuracy = model.calculate_accuracy(X_test, y_test)
    print(f"\n📊 Final Test Accuracy: {final_accuracy:.1%}")
    
    return model, history

def test_digit_recognition():
    """Test digit recognition"""
    print("\n\n🔢 Testing Digit Recognition Neural Network")
    print("=" * 50)
    
    # Generate synthetic MNIST data
    print("📊 Generating synthetic MNIST dataset...")
    X, y = DataGenerator.generate_synthetic_mnist(n_samples=1000, image_size=28)
    
    # Split data
    split_idx = int(0.8 * len(X))
    indices = np.random.permutation(len(X))
    
    X_train = X[indices[:split_idx]]
    y_train = y[indices[:split_idx]]
    X_test = X[indices[split_idx:]]
    y_test = y[indices[split_idx:]]
    
    print(f"📈 Dataset: {len(X_train)} train, {len(X_test)} test samples")
    print(f"🖼️ Image size: 28x28 = {X.shape[1]} pixels")
    
    # Create network
    config = TrainingConfig(
        learning_rate=0.01,
        epochs=100,
        batch_size=32,
        early_stopping_patience=20
    )
    
    model = UnifiedNeuralNetwork(
        architecture=[784, 128, 64, 10],
        activation_function='relu',
        output_activation='softmax',
        config=config
    )
    
    print(f"🏗️ Network Architecture: {model.architecture}")
    
    # Train model
    print("\n🚀 Training network...")
    start_time = time.time()
    
    history = model.train(X_train, y_train, X_test, y_test, verbose=True)
    
    training_time = time.time() - start_time
    print(f"\n⏱️ Training completed in {training_time:.2f} seconds")
    
    # Test individual predictions
    print("\n🧪 Testing individual digit predictions...")
    
    for digit in range(10):
        # Find samples of this digit
        digit_indices = np.where(np.argmax(y_test, axis=1) == digit)[0]
        
        if len(digit_indices) > 0:
            # Test first sample of this digit
            sample_idx = digit_indices[0]
            test_image = X_test[sample_idx]
            
            result = model.predict_single(test_image)
            predicted = result['predicted_class']
            confidence = result['confidence']
            
            status = "✅" if predicted == digit else "❌"
            print(f"{status} Digit {digit}: Predicted {predicted} (confidence: {confidence:.1%})")
    
    # Calculate final accuracy
    final_accuracy = model.calculate_accuracy(X_test, y_test)
    print(f"\n📊 Final Test Accuracy: {final_accuracy:.1%}")
    
    return model, history

def benchmark_performance():
    """Benchmark neural network performance"""
    print("\n\n⚡ Performance Benchmark")
    print("=" * 50)
    
    # Test different architectures
    architectures = [
        [2, 4, 1],      # Simple
        [2, 8, 4, 1],   # Medium
        [2, 16, 8, 1],  # Complex
    ]
    
    results = []
    
    for arch in architectures:
        print(f"\n🏗️ Testing architecture: {arch}")
        
        # Generate data
        X, y = DataGenerator.generate_xor_data(n_samples=800, noise=0.1)
        split_idx = int(0.8 * len(X))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Create and train model
        config = TrainingConfig(learning_rate=0.1, epochs=200, batch_size=16)
        model = UnifiedNeuralNetwork(arch, 'sigmoid', 'sigmoid', config)
        
        start_time = time.time()
        history = model.train(X_train, y_train, X_test, y_test, verbose=False)
        training_time = time.time() - start_time
        
        # Calculate metrics
        final_accuracy = model.calculate_accuracy(X_test, y_test)
        total_params = sum(w.size for w in model.weights) + sum(b.size for b in model.biases)
        
        results.append({
            'architecture': arch,
            'accuracy': final_accuracy,
            'training_time': training_time,
            'parameters': total_params,
            'epochs': len(history['loss'])
        })
        
        print(f"   📊 Accuracy: {final_accuracy:.1%}")
        print(f"   ⏱️ Training time: {training_time:.2f}s")
        print(f"   🔢 Parameters: {total_params}")
    
    # Summary
    print(f"\n📋 Benchmark Summary:")
    print("-" * 60)
    print(f"{'Architecture':<15} {'Accuracy':<10} {'Time':<8} {'Params':<8}")
    print("-" * 60)
    
    for result in results:
        arch_str = str(result['architecture'])
        print(f"{arch_str:<15} {result['accuracy']:<10.1%} {result['training_time']:<8.1f} {result['parameters']:<8}")

def test_activation_functions():
    """Test different activation functions"""
    print("\n\n🔧 Testing Activation Functions")
    print("=" * 50)
    
    activations = ['sigmoid', 'tanh', 'relu', 'leaky_relu']
    results = []
    
    for activation in activations:
        print(f"\n🧪 Testing {activation} activation...")
        
        # Generate XOR data
        X, y = DataGenerator.generate_xor_data(n_samples=800, noise=0.1)
        split_idx = int(0.8 * len(X))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Create and train model
        config = TrainingConfig(learning_rate=0.1, epochs=300, batch_size=16)
        model = UnifiedNeuralNetwork([2, 8, 4, 1], activation, 'sigmoid', config)
        
        start_time = time.time()
        history = model.train(X_train, y_train, X_test, y_test, verbose=False)
        training_time = time.time() - start_time
        
        # Calculate metrics
        final_accuracy = model.calculate_accuracy(X_test, y_test)
        final_loss = history['val_loss'][-1] if history['val_loss'] else 0
        
        results.append({
            'activation': activation,
            'accuracy': final_accuracy,
            'loss': final_loss,
            'time': training_time,
            'epochs': len(history['loss'])
        })
        
        print(f"   📊 Accuracy: {final_accuracy:.1%}")
        print(f"   📉 Final Loss: {final_loss:.4f}")
        print(f"   ⏱️ Time: {training_time:.2f}s")
    
    # Summary
    print(f"\n📋 Activation Function Comparison:")
    print("-" * 70)
    print(f"{'Function':<12} {'Accuracy':<10} {'Loss':<10} {'Time':<8} {'Epochs':<8}")
    print("-" * 70)
    
    for result in results:
        print(f"{result['activation']:<12} {result['accuracy']:<10.1%} {result['loss']:<10.4f} {result['time']:<8.1f} {result['epochs']:<8}")

def main():
    """Main demo function"""
    print("🧠 Neural Network Full Python Implementation Demo")
    print("=" * 60)
    print("This demo tests the neural network implementation without external dependencies")
    print("=" * 60)
    
    try:
        # Test XOR
        xor_model, xor_history = test_xor_network()
        
        # Test Digit Recognition
        digit_model, digit_history = test_digit_recognition()
        
        # Performance benchmark
        benchmark_performance()
        
        # Test activation functions
        test_activation_functions()
        
        print("\n🎉 All tests completed successfully!")
        print("\n💡 Next steps:")
        print("   1. Install Streamlit: pip install streamlit")
        print("   2. Run interactive app: streamlit run streamlit_app.py")
        print("   3. Or install all dependencies: pip install -r requirements.txt")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
