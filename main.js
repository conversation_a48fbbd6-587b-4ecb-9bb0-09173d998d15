// Main application logic
class NeuralNetworkApp {
    constructor() {
        this.network = null;
        this.networkVisualizer = new NetworkVisualizer('network-canvas');
        this.chartVisualizer = new ChartVisualizer('loss-chart');
        this.datasetVisualizer = new DatasetVisualizer('dataset-canvas');
        
        this.isTraining = false;
        this.trainingInterval = null;
        
        // XOR dataset
        this.trainingData = [
            { input: [0, 0], output: [0] },
            { input: [0, 1], output: [1] },
            { input: [1, 0], output: [1] },
            { input: [1, 1], output: [0] }
        ];
        
        this.initializeNetwork();
        this.setupEventListeners();
        this.updateDisplay();
    }
    
    initializeNetwork() {
        const hiddenNeurons = parseInt(document.getElementById('hidden-neurons').value);
        const learningRate = parseFloat(document.getElementById('learning-rate').value);
        const activation = document.getElementById('activation').value;
        
        this.network = new NeuralNetwork(2, hiddenNeurons, 1, learningRate, activation);
        
        // Initialize with some dummy data to show network structure
        this.network.forward([0.5, 0.5]);
    }
    
    setupEventListeners() {
        // Control panel events
        document.getElementById('learning-rate').addEventListener('input', (e) => {
            document.getElementById('lr-value').textContent = e.target.value;
            if (this.network) {
                this.network.learningRate = parseFloat(e.target.value);
            }
        });
        
        document.getElementById('hidden-neurons').addEventListener('input', (e) => {
            document.getElementById('hn-value').textContent = e.target.value;
            document.getElementById('hidden-desc').textContent = `${e.target.value} neurons`;
            this.initializeNetwork();
            this.updateDisplay();
        });
        
        document.getElementById('activation').addEventListener('change', (e) => {
            this.initializeNetwork();
            this.updateDisplay();
        });
        
        // Button events
        document.getElementById('train-btn').addEventListener('click', () => {
            this.toggleTraining();
        });
        
        document.getElementById('reset-btn').addEventListener('click', () => {
            this.resetNetwork();
        });
        
        document.getElementById('test-btn').addEventListener('click', () => {
            this.testNetwork();
        });
        
        document.getElementById('predict-btn').addEventListener('click', () => {
            this.makePrediction();
        });
    }
    
    toggleTraining() {
        const trainBtn = document.getElementById('train-btn');
        
        if (!this.isTraining) {
            this.startTraining();
            trainBtn.textContent = '⏸️ Pause Training';
            trainBtn.classList.remove('primary');
            trainBtn.classList.add('secondary');
        } else {
            this.stopTraining();
            trainBtn.textContent = '🚀 Start Training';
            trainBtn.classList.remove('secondary');
            trainBtn.classList.add('primary');
        }
    }
    
    startTraining() {
        this.isTraining = true;
        this.networkVisualizer.startAnimation();
        
        this.trainingInterval = setInterval(() => {
            // Train for one epoch
            for (let i = 0; i < this.trainingData.length; i++) {
                const data = this.trainingData[i];
                this.network.backward(data.input, data.output);
            }
            
            // Calculate and store loss
            let totalLoss = 0;
            for (let i = 0; i < this.trainingData.length; i++) {
                const data = this.trainingData[i];
                const output = this.network.forward(data.input);
                totalLoss += Math.pow(data.output[0] - output[0], 2);
            }
            
            const avgLoss = totalLoss / this.trainingData.length;
            this.network.lossHistory.push(avgLoss);
            this.network.epoch++;
            
            this.updateDisplay();
            
            // Auto-stop if converged
            if (avgLoss < 0.001) {
                this.stopTraining();
                this.showMessage('Training completed! Network has converged.', 'success');
            }
            
            // Auto-stop after 10000 epochs
            if (this.network.epoch >= 10000) {
                this.stopTraining();
                this.showMessage('Training stopped after 10000 epochs.', 'info');
            }
        }, 50); // Train every 50ms for smooth animation
    }
    
    stopTraining() {
        this.isTraining = false;
        this.networkVisualizer.stopAnimation();
        
        if (this.trainingInterval) {
            clearInterval(this.trainingInterval);
            this.trainingInterval = null;
        }
        
        const trainBtn = document.getElementById('train-btn');
        trainBtn.textContent = '🚀 Start Training';
        trainBtn.classList.remove('secondary');
        trainBtn.classList.add('primary');
    }
    
    resetNetwork() {
        this.stopTraining();
        this.initializeNetwork();
        this.updateDisplay();
        this.showMessage('Network has been reset!', 'info');
    }
    
    testNetwork() {
        if (!this.network) return;
        
        let correct = 0;
        const results = [];
        
        for (let i = 0; i < this.trainingData.length; i++) {
            const data = this.trainingData[i];
            const prediction = this.network.predict(data.input);
            const predicted = prediction[0] > 0.5 ? 1 : 0;
            const actual = data.output[0];
            
            results.push({
                input: data.input,
                predicted: prediction[0],
                actual: actual,
                correct: predicted === actual
            });
            
            if (predicted === actual) correct++;
        }
        
        const accuracy = (correct / this.trainingData.length) * 100;
        
        console.log('Test Results:', results);
        this.showMessage(`Test completed! Accuracy: ${accuracy.toFixed(1)}%`, 'success');
    }
    
    makePrediction() {
        const x = parseFloat(document.getElementById('test-x').value);
        const y = parseFloat(document.getElementById('test-y').value);
        
        if (isNaN(x) || isNaN(y)) {
            this.showMessage('Please enter valid numbers for X and Y', 'error');
            return;
        }
        
        const prediction = this.network.predict([x, y]);
        const result = prediction[0];
        
        document.getElementById('prediction-value').textContent = result.toFixed(4);
        
        // Update network visualization with current input
        this.network.forward([x, y]);
        this.updateDisplay();
    }
    
    updateDisplay() {
        if (!this.network) return;
        
        // Update network visualization
        this.networkVisualizer.drawNetwork(this.network);
        
        // Update loss chart
        this.chartVisualizer.drawLossChart(this.network.lossHistory);
        
        // Update dataset visualization
        this.datasetVisualizer.drawXORDataset();
        
        // Update stats
        document.getElementById('epoch-count').textContent = this.network.epoch;
        
        const currentLoss = this.network.lossHistory.length > 0 
            ? this.network.lossHistory[this.network.lossHistory.length - 1] 
            : 0;
        document.getElementById('loss-value').textContent = currentLoss.toFixed(4);
        
        const accuracy = this.network.calculateAccuracy(this.trainingData);
        document.getElementById('accuracy-value').textContent = `${accuracy.toFixed(1)}%`;
    }
    
    showMessage(message, type = 'info') {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        // Add styles
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        `;
        
        switch (type) {
            case 'success':
                toast.style.backgroundColor = '#48bb78';
                break;
            case 'error':
                toast.style.backgroundColor = '#f56565';
                break;
            case 'info':
            default:
                toast.style.backgroundColor = '#667eea';
                break;
        }
        
        document.body.appendChild(toast);
        
        // Remove after 3 seconds
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
}

// Add CSS for toast animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new NeuralNetworkApp();
});
