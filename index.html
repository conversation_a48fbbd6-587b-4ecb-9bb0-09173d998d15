<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neural Network Visualization</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>🧠 Neural Network Simulator</h1>
            <p>Interactive visualization of neural network training and inference</p>
        </header>

        <div class="main-content">
            <!-- Control Panel -->
            <div class="control-panel">
                <h3>🎛️ Control Panel</h3>
                
                <div class="control-group">
                    <label for="learning-rate">Learning Rate:</label>
                    <input type="range" id="learning-rate" min="0.01" max="1" step="0.01" value="0.1">
                    <span id="lr-value">0.1</span>
                </div>

                <div class="control-group">
                    <label for="hidden-neurons">Hidden Neurons:</label>
                    <input type="range" id="hidden-neurons" min="2" max="8" step="1" value="4">
                    <span id="hn-value">4</span>
                </div>

                <div class="control-group">
                    <label for="activation">Activation Function:</label>
                    <select id="activation">
                        <option value="sigmoid">Sigmoid</option>
                        <option value="tanh">Tanh</option>
                        <option value="relu">ReLU</option>
                    </select>
                </div>

                <div class="button-group">
                    <button id="train-btn" class="btn primary">🚀 Start Training</button>
                    <button id="reset-btn" class="btn secondary">🔄 Reset Network</button>
                    <button id="test-btn" class="btn tertiary">🧪 Test Network</button>
                </div>

                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-label">Epoch:</span>
                        <span id="epoch-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Loss:</span>
                        <span id="loss-value">0.000</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Accuracy:</span>
                        <span id="accuracy-value">0%</span>
                    </div>
                </div>
            </div>

            <!-- Neural Network Visualization -->
            <div class="network-container">
                <h3>🔗 Network Architecture</h3>
                <canvas id="network-canvas" width="600" height="400"></canvas>
                
                <div class="network-info">
                    <div class="layer-info">
                        <span class="layer-label">Input Layer</span>
                        <span class="layer-desc">2 neurons (X, Y)</span>
                    </div>
                    <div class="layer-info">
                        <span class="layer-label">Hidden Layer</span>
                        <span class="layer-desc" id="hidden-desc">4 neurons</span>
                    </div>
                    <div class="layer-info">
                        <span class="layer-label">Output Layer</span>
                        <span class="layer-desc">1 neuron (prediction)</span>
                    </div>
                </div>
            </div>

            <!-- Loss Chart -->
            <div class="chart-container">
                <h3>📊 Training Progress</h3>
                <canvas id="loss-chart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Dataset Visualization -->
        <div class="dataset-section">
            <h3>📈 Dataset Visualization</h3>
            <div class="dataset-container">
                <canvas id="dataset-canvas" width="300" height="300"></canvas>
                <div class="dataset-info">
                    <h4>XOR Problem</h4>
                    <div class="data-points">
                        <div class="data-point">
                            <span class="input">[0, 0]</span>
                            <span class="arrow">→</span>
                            <span class="output">0</span>
                        </div>
                        <div class="data-point">
                            <span class="input">[0, 1]</span>
                            <span class="arrow">→</span>
                            <span class="output">1</span>
                        </div>
                        <div class="data-point">
                            <span class="input">[1, 0]</span>
                            <span class="arrow">→</span>
                            <span class="output">1</span>
                        </div>
                        <div class="data-point">
                            <span class="input">[1, 1]</span>
                            <span class="arrow">→</span>
                            <span class="output">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Section -->
        <div class="test-section">
            <h3>🧪 Test Your Network</h3>
            <div class="test-inputs">
                <div class="input-group">
                    <label for="test-x">X:</label>
                    <input type="number" id="test-x" min="0" max="1" step="0.1" value="0.5">
                </div>
                <div class="input-group">
                    <label for="test-y">Y:</label>
                    <input type="number" id="test-y" min="0" max="1" step="0.1" value="0.5">
                </div>
                <button id="predict-btn" class="btn primary">Predict</button>
            </div>
            <div class="prediction-result">
                <span>Prediction: </span>
                <span id="prediction-value">-</span>
            </div>
        </div>
    </div>

    <script src="neural-network.js"></script>
    <script src="visualization.js"></script>
    <script src="main.js"></script>
</body>
</html>
