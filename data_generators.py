"""
Data Generators for Neural Network Training
Includes XOR, synthetic MNIST, and other datasets
"""

import numpy as np
# import matplotlib.pyplot as plt  # Optional dependency
# from sklearn.datasets import make_classification, make_circles, make_moons  # Optional dependency
from typing import Tuple, List
# import cv2  # Optional dependency

class DataGenerator:
    """Collection of data generators for neural network training"""
    
    @staticmethod
    def generate_xor_data(n_samples: int = 1000, noise: float = 0.1) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate XOR dataset
        
        Args:
            n_samples: Number of samples to generate
            noise: Amount of noise to add
            
        Returns:
            X: Input features (n_samples, 2)
            y: Target labels (n_samples, 1)
        """
        # Generate base XOR patterns
        patterns = np.array([[0, 0], [0, 1], [1, 0], [1, 1]])
        labels = np.array([[0], [1], [1], [0]])
        
        # Repeat patterns and add noise
        n_per_pattern = n_samples // 4
        X = []
        y = []
        
        for i, (pattern, label) in enumerate(zip(patterns, labels)):
            # Generate samples around each pattern
            samples = np.random.normal(pattern, noise, (n_per_pattern, 2))
            # Clip to [0, 1] range
            samples = np.clip(samples, 0, 1)
            
            X.extend(samples)
            y.extend([label] * n_per_pattern)
        
        # Add remaining samples if n_samples is not divisible by 4
        remaining = n_samples - len(X)
        if remaining > 0:
            for i in range(remaining):
                pattern_idx = i % 4
                sample = np.random.normal(patterns[pattern_idx], noise, 2)
                sample = np.clip(sample, 0, 1)
                X.append(sample)
                y.append(labels[pattern_idx])
        
        return np.array(X), np.array(y)
    
    @staticmethod
    def generate_synthetic_mnist(n_samples: int = 1000, image_size: int = 28) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate synthetic MNIST-like digit data
        
        Args:
            n_samples: Number of samples to generate
            image_size: Size of square images
            
        Returns:
            X: Flattened images (n_samples, image_size^2)
            y: One-hot encoded labels (n_samples, 10)
        """
        X = []
        y = []
        
        samples_per_digit = n_samples // 10
        
        for digit in range(10):
            for _ in range(samples_per_digit):
                # Create image
                image = DataGenerator._create_digit_pattern(digit, image_size)
                
                # Add noise and variations
                image = DataGenerator._add_variations(image)
                
                # Flatten image
                X.append(image.flatten())
                
                # One-hot encode label
                label = np.zeros(10)
                label[digit] = 1
                y.append(label)
        
        # Add remaining samples
        remaining = n_samples - len(X)
        for i in range(remaining):
            digit = i % 10
            image = DataGenerator._create_digit_pattern(digit, image_size)
            image = DataGenerator._add_variations(image)
            X.append(image.flatten())
            
            label = np.zeros(10)
            label[digit] = 1
            y.append(label)
        
        return np.array(X), np.array(y)
    
    @staticmethod
    def _create_digit_pattern(digit: int, size: int = 28) -> np.ndarray:
        """Create a basic pattern for each digit"""
        image = np.zeros((size, size))
        center = size // 2
        
        if digit == 0:  # Circle
            y, x = np.ogrid[:size, :size]
            mask = (x - center)**2 + (y - center)**2 <= (size//3)**2
            outer_mask = (x - center)**2 + (y - center)**2 >= (size//4)**2
            image[mask & outer_mask] = 1
            
        elif digit == 1:  # Vertical line
            image[size//4:3*size//4, center-1:center+2] = 1
            
        elif digit == 2:  # Horizontal lines
            image[size//4:size//4+3, size//4:3*size//4] = 1
            image[center-1:center+2, size//4:3*size//4] = 1
            image[3*size//4-2:3*size//4+1, size//4:3*size//4] = 1
            
        elif digit == 3:  # Three horizontal lines
            image[size//4:size//4+2, size//3:2*size//3] = 1
            image[center-1:center+1, size//3:2*size//3] = 1
            image[3*size//4-1:3*size//4+1, size//3:2*size//3] = 1
            
        elif digit == 4:  # L shape
            image[size//4:3*size//4, size//3:size//3+2] = 1
            image[center-1:center+1, size//3:2*size//3] = 1
            
        elif digit == 5:  # S shape
            image[size//4:size//4+2, size//3:2*size//3] = 1
            image[center-1:center+1, size//3:center] = 1
            image[3*size//4-1:3*size//4+1, center:2*size//3] = 1
            
        elif digit == 6:  # P shape
            image[size//4:3*size//4, size//3:size//3+2] = 1
            image[size//4:size//4+2, size//3:center] = 1
            image[center-1:center+1, size//3:center] = 1
            
        elif digit == 7:  # 7 shape
            image[size//4:size//4+2, size//3:2*size//3] = 1
            # Diagonal line
            for i in range(size//4, 3*size//4):
                j = 2*size//3 - (i - size//4) // 2
                if 0 <= j < size:
                    image[i, j] = 1
                    
        elif digit == 8:  # Two circles
            # Upper circle
            y, x = np.ogrid[:size, :size]
            upper_center = size//3
            mask1 = (x - center)**2 + (y - upper_center)**2 <= (size//6)**2
            outer_mask1 = (x - center)**2 + (y - upper_center)**2 >= (size//8)**2
            image[mask1 & outer_mask1] = 1
            
            # Lower circle
            lower_center = 2*size//3
            mask2 = (x - center)**2 + (y - lower_center)**2 <= (size//6)**2
            outer_mask2 = (x - center)**2 + (y - lower_center)**2 >= (size//8)**2
            image[mask2 & outer_mask2] = 1
            
        elif digit == 9:  # Circle with tail
            y, x = np.ogrid[:size, :size]
            upper_center = size//3
            mask = (x - center)**2 + (y - upper_center)**2 <= (size//6)**2
            outer_mask = (x - center)**2 + (y - upper_center)**2 >= (size//8)**2
            image[mask & outer_mask] = 1
            # Tail
            image[center:3*size//4, center+size//6:center+size//6+2] = 1
        
        return image
    
    @staticmethod
    def _add_variations(image: np.ndarray) -> np.ndarray:
        """Add variations to make the digit more realistic"""
        # Add Gaussian noise
        noise = np.random.normal(0, 0.05, image.shape)
        image = image + noise
        
        # Simple transformations without cv2
        # Random rotation (simple implementation)
        if np.random.random() > 0.5:
            # Simple rotation by 90 degrees occasionally
            if np.random.random() > 0.7:
                image = np.rot90(image)

        # Random translation (simple shift)
        if np.random.random() > 0.5:
            tx = np.random.randint(-2, 3)
            ty = np.random.randint(-2, 3)
            # Simple translation by shifting array
            if tx != 0 or ty != 0:
                image = np.roll(image, (ty, tx), axis=(0, 1))
        
        # Clip values
        image = np.clip(image, 0, 1)
        
        return image
    
    @staticmethod
    def generate_classification_data(n_samples: int = 1000,
                                   n_features: int = 2,
                                   n_classes: int = 2,
                                   dataset_type: str = 'random') -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate various classification datasets

        Args:
            n_samples: Number of samples
            n_features: Number of features
            n_classes: Number of classes
            dataset_type: Type of dataset ('random', 'circles', 'moons')

        Returns:
            X: Features
            y: Labels (one-hot encoded)
        """
        if dataset_type == 'circles':
            # Generate concentric circles
            X = []
            y_raw = []
            for i in range(n_samples):
                if np.random.random() > 0.5:
                    # Outer circle
                    angle = np.random.uniform(0, 2 * np.pi)
                    radius = np.random.uniform(0.7, 1.0)
                    x = radius * np.cos(angle)
                    y = radius * np.sin(angle)
                    label = 1
                else:
                    # Inner circle
                    angle = np.random.uniform(0, 2 * np.pi)
                    radius = np.random.uniform(0, 0.5)
                    x = radius * np.cos(angle)
                    y = radius * np.sin(angle)
                    label = 0

                # Add noise
                x += np.random.normal(0, 0.1)
                y += np.random.normal(0, 0.1)

                X.append([x, y])
                y_raw.append(label)

            X = np.array(X)
            y_raw = np.array(y_raw)

        elif dataset_type == 'moons':
            # Generate two interleaving half circles
            X = []
            y_raw = []
            for i in range(n_samples):
                if np.random.random() > 0.5:
                    # Upper moon
                    angle = np.random.uniform(0, np.pi)
                    x = np.cos(angle)
                    y = np.sin(angle)
                    label = 1
                else:
                    # Lower moon
                    angle = np.random.uniform(0, np.pi)
                    x = 1 - np.cos(angle)
                    y = 0.5 - np.sin(angle)
                    label = 0

                # Add noise
                x += np.random.normal(0, 0.1)
                y += np.random.normal(0, 0.1)

                X.append([x, y])
                y_raw.append(label)

            X = np.array(X)
            y_raw = np.array(y_raw)

        else:  # random
            # Generate random classification data
            X = np.random.randn(n_samples, n_features)

            # Create separable classes
            centers = np.random.randn(n_classes, n_features) * 2
            y_raw = np.random.randint(0, n_classes, n_samples)

            for i in range(n_samples):
                X[i] += centers[y_raw[i]] + np.random.normal(0, 0.5, n_features)

        # One-hot encode labels
        y = np.zeros((len(y_raw), n_classes))
        y[np.arange(len(y_raw)), y_raw] = 1

        return X, y
    
    @staticmethod
    def generate_regression_data(n_samples: int = 1000, 
                               n_features: int = 1,
                               noise: float = 0.1,
                               function_type: str = 'linear') -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate regression datasets
        
        Args:
            n_samples: Number of samples
            n_features: Number of input features
            noise: Amount of noise to add
            function_type: Type of function ('linear', 'quadratic', 'sine', 'complex')
            
        Returns:
            X: Input features
            y: Target values
        """
        if n_features == 1:
            X = np.random.uniform(-2, 2, (n_samples, 1))
            
            if function_type == 'linear':
                y = 2 * X + 1
            elif function_type == 'quadratic':
                y = X**2 + 0.5 * X
            elif function_type == 'sine':
                y = np.sin(2 * np.pi * X)
            elif function_type == 'complex':
                y = np.sin(2 * np.pi * X) + 0.5 * np.cos(4 * np.pi * X)
            else:
                y = X  # Default to linear
                
        else:
            X = np.random.uniform(-2, 2, (n_samples, n_features))
            # Multi-dimensional linear function
            weights = np.random.uniform(-1, 1, (n_features, 1))
            y = np.dot(X, weights)
        
        # Add noise
        y += np.random.normal(0, noise, y.shape)
        
        return X, y
    
    @staticmethod
    def visualize_data(X: np.ndarray, y: np.ndarray, title: str = "Dataset"):
        """Visualize 2D dataset (requires matplotlib)"""
        try:
            import matplotlib.pyplot as plt

            if X.shape[1] != 2:
                print("Visualization only available for 2D data")
                return

            plt.figure(figsize=(8, 6))

            if len(y.shape) > 1 and y.shape[1] > 1:
                # One-hot encoded labels
                labels = np.argmax(y, axis=1)
            else:
                labels = y.flatten()

            unique_labels = np.unique(labels)
            colors = plt.cm.Set1(np.linspace(0, 1, len(unique_labels)))

            for i, label in enumerate(unique_labels):
                mask = labels == label
                plt.scatter(X[mask, 0], X[mask, 1],
                           c=[colors[i]], label=f'Class {int(label)}', alpha=0.7)

            plt.xlabel('Feature 1')
            plt.ylabel('Feature 2')
            plt.title(title)
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.show()

        except ImportError:
            print("Matplotlib not available. Skipping visualization.")

    @staticmethod
    def visualize_digits(X: np.ndarray, y: np.ndarray, n_samples: int = 10, image_size: int = 28):
        """Visualize digit samples (requires matplotlib)"""
        try:
            import matplotlib.pyplot as plt

            fig, axes = plt.subplots(2, 5, figsize=(12, 6))
            axes = axes.flatten()

            # Get one sample for each digit
            for digit in range(10):
                if len(y.shape) > 1:
                    # One-hot encoded
                    digit_indices = np.where(np.argmax(y, axis=1) == digit)[0]
                else:
                    digit_indices = np.where(y == digit)[0]

                if len(digit_indices) > 0:
                    sample_idx = digit_indices[0]
                    image = X[sample_idx].reshape(image_size, image_size)

                    axes[digit].imshow(image, cmap='gray')
                    axes[digit].set_title(f'Digit {digit}')
                    axes[digit].axis('off')

            plt.tight_layout()
            plt.show()

        except ImportError:
            print("Matplotlib not available. Skipping visualization.")
