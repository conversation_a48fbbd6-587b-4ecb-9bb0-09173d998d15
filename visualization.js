class NetworkVisualizer {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.animationId = null;
        this.animationProgress = 0;
        this.isAnimating = false;
    }
    
    drawNetwork(network) {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        const state = network.getNetworkState();
        const layers = [
            { neurons: state.inputActivations, x: 100, label: 'Input' },
            { neurons: state.hiddenActivations, x: 300, label: 'Hidden' },
            { neurons: state.outputActivations, x: 500, label: 'Output' }
        ];
        
        // Draw connections first (so they appear behind neurons)
        this.drawConnections(network, layers);
        
        // Draw neurons
        layers.forEach((layer, layerIndex) => {
            this.drawLayer(layer, layerIndex);
        });
        
        // Draw labels
        this.drawLabels(layers);
    }
    
    drawLayer(layer, layerIndex) {
        const neuronCount = layer.neurons.length;
        const startY = (this.canvas.height - (neuronCount * 60)) / 2;
        
        layer.neurons.forEach((activation, neuronIndex) => {
            const x = layer.x;
            const y = startY + neuronIndex * 60 + 30;
            
            // Neuron circle
            const intensity = Math.abs(activation || 0);
            const radius = 20 + intensity * 10;
            
            // Gradient based on activation
            const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, radius);
            if (activation > 0) {
                gradient.addColorStop(0, `rgba(72, 187, 120, ${0.3 + intensity * 0.7})`);
                gradient.addColorStop(1, `rgba(72, 187, 120, ${0.1 + intensity * 0.3})`);
            } else {
                gradient.addColorStop(0, `rgba(245, 101, 101, ${0.3 + intensity * 0.7})`);
                gradient.addColorStop(1, `rgba(245, 101, 101, ${0.1 + intensity * 0.3})`);
            }
            
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
            this.ctx.fill();
            
            // Neuron border
            this.ctx.strokeStyle = '#4a5568';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
            
            // Activation value text
            this.ctx.fillStyle = '#2d3748';
            this.ctx.font = '12px Inter';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(activation ? activation.toFixed(2) : '0.00', x, y + 4);
        });
    }
    
    drawConnections(network, layers) {
        const state = network.getNetworkState();
        
        // Input to hidden connections
        this.drawConnectionsBetweenLayers(
            layers[0], layers[1], 
            state.weightsInputHidden,
            state.inputActivations,
            state.hiddenActivations
        );
        
        // Hidden to output connections
        this.drawConnectionsBetweenLayers(
            layers[1], layers[2], 
            state.weightsHiddenOutput,
            state.hiddenActivations,
            state.outputActivations
        );
    }
    
    drawConnectionsBetweenLayers(fromLayer, toLayer, weights, fromActivations, toActivations) {
        const fromStartY = (this.canvas.height - (fromLayer.neurons.length * 60)) / 2;
        const toStartY = (this.canvas.height - (toLayer.neurons.length * 60)) / 2;
        
        fromLayer.neurons.forEach((_, fromIndex) => {
            toLayer.neurons.forEach((_, toIndex) => {
                const fromX = fromLayer.x + 20;
                const fromY = fromStartY + fromIndex * 60 + 30;
                const toX = toLayer.x - 20;
                const toY = toStartY + toIndex * 60 + 30;
                
                const weight = weights[fromIndex][toIndex];
                const weightMagnitude = Math.abs(weight);
                
                // Connection color and thickness based on weight
                const alpha = Math.min(weightMagnitude * 2, 1);
                const thickness = Math.max(1, weightMagnitude * 3);
                
                if (weight > 0) {
                    this.ctx.strokeStyle = `rgba(72, 187, 120, ${alpha})`;
                } else {
                    this.ctx.strokeStyle = `rgba(245, 101, 101, ${alpha})`;
                }
                
                this.ctx.lineWidth = thickness;
                this.ctx.beginPath();
                this.ctx.moveTo(fromX, fromY);
                this.ctx.lineTo(toX, toY);
                this.ctx.stroke();
                
                // Animate signal flow during training
                if (this.isAnimating) {
                    this.drawSignalFlow(fromX, fromY, toX, toY, fromActivations[fromIndex] || 0);
                }
            });
        });
    }
    
    drawSignalFlow(fromX, fromY, toX, toY, activation) {
        const progress = (this.animationProgress % 100) / 100;
        const x = fromX + (toX - fromX) * progress;
        const y = fromY + (toY - fromY) * progress;
        
        const intensity = Math.abs(activation);
        const radius = 3 + intensity * 2;
        
        this.ctx.fillStyle = `rgba(102, 126, 234, ${0.8 * intensity})`;
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
        this.ctx.fill();
    }
    
    drawLabels(layers) {
        this.ctx.fillStyle = '#4a5568';
        this.ctx.font = '14px Inter';
        this.ctx.textAlign = 'center';
        
        layers.forEach(layer => {
            this.ctx.fillText(layer.label, layer.x, 30);
        });
    }
    
    startAnimation() {
        this.isAnimating = true;
        this.animate();
    }
    
    stopAnimation() {
        this.isAnimating = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }
    
    animate() {
        if (!this.isAnimating) return;
        
        this.animationProgress += 2;
        this.animationId = requestAnimationFrame(() => this.animate());
    }
}

class ChartVisualizer {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.maxDataPoints = 100;
    }
    
    drawLossChart(lossHistory) {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (lossHistory.length === 0) return;
        
        const padding = 40;
        const chartWidth = this.canvas.width - 2 * padding;
        const chartHeight = this.canvas.height - 2 * padding;
        
        // Find min and max values
        const maxLoss = Math.max(...lossHistory);
        const minLoss = Math.min(...lossHistory);
        const range = maxLoss - minLoss || 1;
        
        // Draw axes
        this.ctx.strokeStyle = '#e2e8f0';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(padding, padding);
        this.ctx.lineTo(padding, this.canvas.height - padding);
        this.ctx.lineTo(this.canvas.width - padding, this.canvas.height - padding);
        this.ctx.stroke();
        
        // Draw grid lines
        this.ctx.strokeStyle = '#f7fafc';
        this.ctx.lineWidth = 1;
        for (let i = 1; i < 5; i++) {
            const y = padding + (chartHeight * i) / 5;
            this.ctx.beginPath();
            this.ctx.moveTo(padding, y);
            this.ctx.lineTo(this.canvas.width - padding, y);
            this.ctx.stroke();
        }
        
        // Draw loss curve
        if (lossHistory.length > 1) {
            this.ctx.strokeStyle = '#667eea';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            
            const dataToShow = lossHistory.slice(-this.maxDataPoints);
            
            dataToShow.forEach((loss, index) => {
                const x = padding + (chartWidth * index) / (dataToShow.length - 1);
                const y = this.canvas.height - padding - ((loss - minLoss) / range) * chartHeight;
                
                if (index === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            });
            
            this.ctx.stroke();
            
            // Draw points
            this.ctx.fillStyle = '#667eea';
            dataToShow.forEach((loss, index) => {
                const x = padding + (chartWidth * index) / (dataToShow.length - 1);
                const y = this.canvas.height - padding - ((loss - minLoss) / range) * chartHeight;
                
                this.ctx.beginPath();
                this.ctx.arc(x, y, 3, 0, 2 * Math.PI);
                this.ctx.fill();
            });
        }
        
        // Draw labels
        this.ctx.fillStyle = '#4a5568';
        this.ctx.font = '12px Inter';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Epochs', this.canvas.width / 2, this.canvas.height - 10);
        
        this.ctx.save();
        this.ctx.translate(15, this.canvas.height / 2);
        this.ctx.rotate(-Math.PI / 2);
        this.ctx.fillText('Loss', 0, 0);
        this.ctx.restore();
        
        // Draw current loss value
        if (lossHistory.length > 0) {
            const currentLoss = lossHistory[lossHistory.length - 1];
            this.ctx.fillStyle = '#667eea';
            this.ctx.font = '14px Inter';
            this.ctx.textAlign = 'right';
            this.ctx.fillText(`Loss: ${currentLoss.toFixed(4)}`, this.canvas.width - padding, padding + 20);
        }
    }
}

class DatasetVisualizer {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
    }
    
    drawXORDataset() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        const padding = 30;
        const size = this.canvas.width - 2 * padding;
        
        // Draw axes
        this.ctx.strokeStyle = '#e2e8f0';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(padding, this.canvas.height - padding);
        this.ctx.lineTo(this.canvas.width - padding, this.canvas.height - padding);
        this.ctx.moveTo(padding, this.canvas.height - padding);
        this.ctx.lineTo(padding, padding);
        this.ctx.stroke();
        
        // XOR data points
        const xorData = [
            { input: [0, 0], output: 0, color: '#f56565' },
            { input: [0, 1], output: 1, color: '#48bb78' },
            { input: [1, 0], output: 1, color: '#48bb78' },
            { input: [1, 1], output: 0, color: '#f56565' }
        ];
        
        xorData.forEach(point => {
            const x = padding + point.input[0] * size;
            const y = this.canvas.height - padding - point.input[1] * size;
            
            this.ctx.fillStyle = point.color;
            this.ctx.beginPath();
            this.ctx.arc(x, y, 12, 0, 2 * Math.PI);
            this.ctx.fill();
            
            this.ctx.strokeStyle = '#2d3748';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
            
            // Label
            this.ctx.fillStyle = 'white';
            this.ctx.font = '12px Inter';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(point.output.toString(), x, y + 4);
        });
        
        // Axis labels
        this.ctx.fillStyle = '#4a5568';
        this.ctx.font = '14px Inter';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('X', this.canvas.width / 2, this.canvas.height - 5);
        
        this.ctx.save();
        this.ctx.translate(10, this.canvas.height / 2);
        this.ctx.rotate(-Math.PI / 2);
        this.ctx.fillText('Y', 0, 0);
        this.ctx.restore();
        
        // Grid
        this.ctx.strokeStyle = '#f7fafc';
        this.ctx.lineWidth = 1;
        this.ctx.setLineDash([5, 5]);
        
        // Vertical grid line
        this.ctx.beginPath();
        this.ctx.moveTo(padding + size, padding);
        this.ctx.lineTo(padding + size, this.canvas.height - padding);
        this.ctx.stroke();
        
        // Horizontal grid line
        this.ctx.beginPath();
        this.ctx.moveTo(padding, padding);
        this.ctx.lineTo(this.canvas.width - padding, padding);
        this.ctx.stroke();
        
        this.ctx.setLineDash([]);
    }
}
