"""
Visualization utilities for neural network analysis
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import networkx as nx
from typing import List, Dict, Any
import pandas as pd

class NetworkVisualizer:
    """Comprehensive neural network visualization tools"""
    
    @staticmethod
    def plot_network_architecture(architecture: List[int], 
                                title: str = "Neural Network Architecture",
                                figsize: tuple = (12, 8)):
        """
        Visualize neural network architecture
        
        Args:
            architecture: List of layer sizes
            title: Plot title
            figsize: Figure size
        """
        fig, ax = plt.subplots(figsize=figsize)
        
        # Calculate positions
        max_neurons = max(architecture)
        layer_positions = np.linspace(0, 10, len(architecture))
        
        # Draw neurons
        for layer_idx, n_neurons in enumerate(architecture):
            x = layer_positions[layer_idx]
            
            # Center neurons vertically
            if n_neurons == 1:
                y_positions = [max_neurons / 2]
            else:
                y_positions = np.linspace(0, max_neurons, n_neurons)
            
            # Draw neurons
            for y in y_positions:
                circle = plt.Circle((x, y), 0.3, color='lightblue', 
                                  ec='darkblue', linewidth=2)
                ax.add_patch(circle)
            
            # Add layer labels
            layer_names = ['Input', 'Hidden', 'Hidden', 'Output']
            if layer_idx < len(layer_names):
                label = layer_names[layer_idx]
            else:
                label = f'Hidden {layer_idx}'
            
            ax.text(x, -1, f'{label}\n({n_neurons})', 
                   ha='center', va='top', fontsize=10, fontweight='bold')
        
        # Draw connections
        for layer_idx in range(len(architecture) - 1):
            x1 = layer_positions[layer_idx]
            x2 = layer_positions[layer_idx + 1]
            
            n1 = architecture[layer_idx]
            n2 = architecture[layer_idx + 1]
            
            y1_positions = np.linspace(0, max_neurons, n1) if n1 > 1 else [max_neurons / 2]
            y2_positions = np.linspace(0, max_neurons, n2) if n2 > 1 else [max_neurons / 2]
            
            for y1 in y1_positions:
                for y2 in y2_positions:
                    ax.plot([x1 + 0.3, x2 - 0.3], [y1, y2], 
                           'gray', alpha=0.3, linewidth=0.5)
        
        ax.set_xlim(-1, 11)
        ax.set_ylim(-2, max_neurons + 1)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def plot_training_history(history: Dict[str, List], figsize: tuple = (15, 5)):
        """
        Plot training history
        
        Args:
            history: Training history dictionary
            figsize: Figure size
        """
        fig, axes = plt.subplots(1, 3, figsize=figsize)
        
        epochs = range(1, len(history['loss']) + 1)
        
        # Loss plot
        axes[0].plot(epochs, history['loss'], 'b-', label='Training Loss', linewidth=2)
        if 'val_loss' in history and history['val_loss']:
            axes[0].plot(epochs, history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
        axes[0].set_title('Model Loss', fontsize=14, fontweight='bold')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Accuracy plot
        axes[1].plot(epochs, history['accuracy'], 'b-', label='Training Accuracy', linewidth=2)
        if 'val_accuracy' in history and history['val_accuracy']:
            axes[1].plot(epochs, history['val_accuracy'], 'r-', label='Validation Accuracy', linewidth=2)
        axes[1].set_title('Model Accuracy', fontsize=14, fontweight='bold')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('Accuracy')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # Training time plot
        if 'epoch_times' in history and history['epoch_times']:
            axes[2].plot(epochs, history['epoch_times'], 'g-', linewidth=2)
            axes[2].set_title('Training Time per Epoch', fontsize=14, fontweight='bold')
            axes[2].set_xlabel('Epoch')
            axes[2].set_ylabel('Time (seconds)')
            axes[2].grid(True, alpha=0.3)
        else:
            axes[2].text(0.5, 0.5, 'No timing data available', 
                        ha='center', va='center', transform=axes[2].transAxes)
            axes[2].set_title('Training Time per Epoch', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def plot_decision_boundary(model, X, y, resolution=100, figsize=(10, 8)):
        """
        Plot decision boundary for 2D classification problems
        
        Args:
            model: Trained neural network model
            X: Input features (2D)
            y: Target labels
            resolution: Resolution of the decision boundary
            figsize: Figure size
        """
        if X.shape[1] != 2:
            print("Decision boundary visualization only available for 2D data")
            return
        
        # Create a mesh
        x_min, x_max = X[:, 0].min() - 0.5, X[:, 0].max() + 0.5
        y_min, y_max = X[:, 1].min() - 0.5, X[:, 1].max() + 0.5
        
        xx, yy = np.meshgrid(np.linspace(x_min, x_max, resolution),
                            np.linspace(y_min, y_max, resolution))
        
        # Make predictions on the mesh
        mesh_points = np.c_[xx.ravel(), yy.ravel()]
        Z = model.predict(mesh_points)
        
        if Z.shape[1] > 1:
            # Multi-class: use argmax
            Z = np.argmax(Z, axis=1)
        else:
            # Binary: threshold at 0.5
            Z = (Z > 0.5).astype(int).flatten()
        
        Z = Z.reshape(xx.shape)
        
        # Plot
        plt.figure(figsize=figsize)
        plt.contourf(xx, yy, Z, alpha=0.4, cmap=plt.cm.RdYlBu)
        
        # Plot data points
        if len(y.shape) > 1 and y.shape[1] > 1:
            labels = np.argmax(y, axis=1)
        else:
            labels = y.flatten()
        
        unique_labels = np.unique(labels)
        colors = plt.cm.Set1(np.linspace(0, 1, len(unique_labels)))
        
        for i, label in enumerate(unique_labels):
            mask = labels == label
            plt.scatter(X[mask, 0], X[mask, 1], 
                       c=[colors[i]], label=f'Class {int(label)}', 
                       edgecolors='black', s=50)
        
        plt.xlabel('Feature 1')
        plt.ylabel('Feature 2')
        plt.title('Decision Boundary')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()
    
    @staticmethod
    def plot_activation_heatmap(activations: List[np.ndarray], 
                              layer_names: List[str] = None,
                              figsize: tuple = (15, 8)):
        """
        Plot heatmap of neuron activations
        
        Args:
            activations: List of activation arrays for each layer
            layer_names: Names of layers
            figsize: Figure size
        """
        if not activations:
            print("No activation data available")
            return
        
        n_layers = len(activations)
        fig, axes = plt.subplots(1, n_layers, figsize=figsize)
        
        if n_layers == 1:
            axes = [axes]
        
        for i, activation in enumerate(activations):
            if len(activation.shape) == 1:
                activation = activation.reshape(-1, 1)
            
            sns.heatmap(activation.T, ax=axes[i], cmap='viridis', 
                       cbar=True, square=False)
            
            if layer_names and i < len(layer_names):
                title = layer_names[i]
            else:
                title = f'Layer {i}'
            
            axes[i].set_title(title)
            axes[i].set_xlabel('Sample')
            axes[i].set_ylabel('Neuron')
        
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def plot_weight_distribution(weights: List[np.ndarray], 
                               layer_names: List[str] = None,
                               figsize: tuple = (15, 5)):
        """
        Plot distribution of weights in each layer
        
        Args:
            weights: List of weight matrices
            layer_names: Names of layers
            figsize: Figure size
        """
        n_layers = len(weights)
        fig, axes = plt.subplots(1, n_layers, figsize=figsize)
        
        if n_layers == 1:
            axes = [axes]
        
        for i, weight_matrix in enumerate(weights):
            axes[i].hist(weight_matrix.flatten(), bins=50, alpha=0.7, 
                        color=f'C{i}', edgecolor='black')
            
            if layer_names and i < len(layer_names):
                title = f'{layer_names[i]} Weights'
            else:
                title = f'Layer {i} Weights'
            
            axes[i].set_title(title)
            axes[i].set_xlabel('Weight Value')
            axes[i].set_ylabel('Frequency')
            axes[i].grid(True, alpha=0.3)
            
            # Add statistics
            mean_weight = np.mean(weight_matrix)
            std_weight = np.std(weight_matrix)
            axes[i].axvline(mean_weight, color='red', linestyle='--', 
                           label=f'Mean: {mean_weight:.3f}')
            axes[i].legend()
        
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def plot_confusion_matrix(y_true, y_pred, class_names=None, figsize=(8, 6)):
        """
        Plot confusion matrix
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            class_names: Names of classes
            figsize: Figure size
        """
        from sklearn.metrics import confusion_matrix
        
        # Convert one-hot to labels if necessary
        if len(y_true.shape) > 1 and y_true.shape[1] > 1:
            y_true = np.argmax(y_true, axis=1)
        if len(y_pred.shape) > 1 and y_pred.shape[1] > 1:
            y_pred = np.argmax(y_pred, axis=1)
        
        cm = confusion_matrix(y_true, y_pred)
        
        plt.figure(figsize=figsize)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=class_names, yticklabels=class_names)
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.show()
    
    @staticmethod
    def create_interactive_network_plot(architecture: List[int], 
                                      weights: List[np.ndarray] = None,
                                      activations: List[np.ndarray] = None):
        """
        Create interactive network visualization using Plotly
        
        Args:
            architecture: Network architecture
            weights: Weight matrices (optional)
            activations: Activation values (optional)
        """
        # Create network graph
        G = nx.DiGraph()
        
        # Add nodes
        node_positions = {}
        node_colors = []
        node_sizes = []
        
        node_id = 0
        for layer_idx, n_neurons in enumerate(architecture):
            for neuron_idx in range(n_neurons):
                G.add_node(node_id)
                
                # Position
                x = layer_idx * 2
                y = neuron_idx - n_neurons / 2
                node_positions[node_id] = (x, y)
                
                # Color based on activation if available
                if activations and layer_idx < len(activations):
                    if len(activations[layer_idx]) > neuron_idx:
                        activation = activations[layer_idx][neuron_idx]
                        node_colors.append(activation)
                    else:
                        node_colors.append(0)
                else:
                    node_colors.append(0.5)
                
                node_sizes.append(20)
                node_id += 1
        
        # Add edges
        node_id = 0
        for layer_idx in range(len(architecture) - 1):
            for i in range(architecture[layer_idx]):
                for j in range(architecture[layer_idx + 1]):
                    source = node_id + i
                    target = node_id + architecture[layer_idx] + j
                    
                    # Edge weight
                    if weights and layer_idx < len(weights):
                        weight = weights[layer_idx][i, j]
                        G.add_edge(source, target, weight=weight)
                    else:
                        G.add_edge(source, target, weight=0.5)
            
            node_id += architecture[layer_idx]
        
        # Create Plotly figure
        edge_x = []
        edge_y = []
        edge_weights = []
        
        for edge in G.edges():
            x0, y0 = node_positions[edge[0]]
            x1, y1 = node_positions[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
            edge_weights.append(G[edge[0]][edge[1]]['weight'])
        
        # Normalize edge weights for visualization
        if edge_weights:
            max_weight = max(abs(w) for w in edge_weights)
            if max_weight > 0:
                edge_weights = [w / max_weight for w in edge_weights]
        
        # Create edge trace
        edge_trace = go.Scatter(x=edge_x, y=edge_y,
                               line=dict(width=2, color='gray'),
                               hoverinfo='none',
                               mode='lines')
        
        # Create node trace
        node_x = [node_positions[node][0] for node in G.nodes()]
        node_y = [node_positions[node][1] for node in G.nodes()]
        
        node_trace = go.Scatter(x=node_x, y=node_y,
                               mode='markers',
                               hoverinfo='text',
                               marker=dict(size=node_sizes,
                                         color=node_colors,
                                         colorscale='Viridis',
                                         showscale=True,
                                         colorbar=dict(title="Activation")))
        
        # Create figure
        fig = go.Figure(data=[edge_trace, node_trace],
                       layout=go.Layout(
                           title='Interactive Neural Network',
                           titlefont_size=16,
                           showlegend=False,
                           hovermode='closest',
                           margin=dict(b=20,l=5,r=5,t=40),
                           annotations=[ dict(
                               text="Neural Network Visualization",
                               showarrow=False,
                               xref="paper", yref="paper",
                               x=0.005, y=-0.002,
                               xanchor="left", yanchor="bottom",
                               font=dict(color="black", size=12)
                           )],
                           xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                           yaxis=dict(showgrid=False, zeroline=False, showticklabels=False)))
        
        return fig
    
    @staticmethod
    def plot_learning_curves(train_sizes, train_scores, val_scores, figsize=(10, 6)):
        """
        Plot learning curves
        
        Args:
            train_sizes: Training set sizes
            train_scores: Training scores
            val_scores: Validation scores
            figsize: Figure size
        """
        plt.figure(figsize=figsize)
        
        train_mean = np.mean(train_scores, axis=1)
        train_std = np.std(train_scores, axis=1)
        val_mean = np.mean(val_scores, axis=1)
        val_std = np.std(val_scores, axis=1)
        
        plt.plot(train_sizes, train_mean, 'o-', color='blue', label='Training Score')
        plt.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, 
                        alpha=0.1, color='blue')
        
        plt.plot(train_sizes, val_mean, 'o-', color='red', label='Validation Score')
        plt.fill_between(train_sizes, val_mean - val_std, val_mean + val_std, 
                        alpha=0.1, color='red')
        
        plt.xlabel('Training Set Size')
        plt.ylabel('Score')
        plt.title('Learning Curves')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()
